/*
Example input:
  "please identify whether or not it is safe to remove the `src\config.py`"

Example output:
  "Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies. Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. please identify whether or not it is safe to remove the `src\config.py`"

*/

{
  "name": "v01.codebaseinstructions",
  "description": "Templates that rephrase inputs into instructions optimized for autonomous coding agents (Cursor, VS Code agent mode, Cline, etc)",
  "config": {
    "chain_mode": true,
    "show_inputs": false,
    "show_system_instructions": false,
    "show_responses": true,
    "minified_output": true,
    "concurrent": true
  },
  "defaults": {
    "prompt": "\"can't tell if it's on purpose. #engineeredchaos\": what would be the perfect hashtag according to the depth of kuci's character while also accounting for virality (to replace \"#engineeredchaos\")?",
    // "models": ["gpt-3.5-turbo"]
    "models": ["gpt-5-chat-latest"]
  },
  "jobs": {
    "a-01_": {"sequence": "1900|3000|3100"},
    "a-02_": {"sequence": "2001"},
    "a-03_": {"sequence": "2004"},
    "a-04_": {"sequence": "3000"},
    "a-05_": {"sequence": "3000|2004:d|3000"},
    "a-06_": {"sequence": "3000|3100-a-c"},
    "a-07_": {"sequence": "3000|3100:a-c"},
    "a-08_": {"sequence": "3001"},
    "a-09_": {"sequence": "3001|3100"},
    "a-10_": {"sequence": "3003-a|3001-a-b|3100-d|3003|3100-d|3100-a-c"},
    "a-11_": {"sequence": "3003-a|3100-d|3001-a-b|3003|3100-d|3100-a-c"},
    "a-12_": {"sequence": "3003|3030|3022|3100-a-c|3030"},
    "a-13_": {"sequence": "3003|3030|3025-a-b|3022|3025-d|3030|3022|3100-a-c|3030"},
    "a-14_": {"sequence": "3020-b|3021-a|3022|3005|3020-c|3021|3020-a|3100-a-c|3016-a|3015-e|3017"},
    "a-15_": {"sequence": "3030|3000"},
    "a-16_": {"sequence": "3030|3022|3000"},
    "a-17_": {"sequence": "3030|3022|3000|3030|3022|3000"},
    "a-18_": {"sequence": "3031|3014"},
    "a-19_": {"sequence": "3036"},
    "a-20_": {"sequence": "3036|3022|3020-c"},
    "a-21_": {"sequence": "3036|3022|3020-c|3036|3022|3036"},
    "a-22_": {"sequence": "3036|3022|3036|3022|3036"},
    "a-23_": {"sequence": "3037|3022|3037"},
    "a-24_": {"sequence": "3100"},
    "a-25_": {"sequence": "3100:a-c"},
  }
}

// [SEQ:1031|1010] 'Although the continual progression of advancements in technology (AI/LLM in particular), the progression has started to gain speed towards exponentials. In recent months and weeks the amount AI services and utilities has not only exploded, but the way they're *used* has transformed to such degree that that we're now able to *utilize their potential*. Services (such as bolt, v0, lovable, replit, etc) and autonomous coding assistants (such as cursor, vscode+augment/cline etc) has made it easy to build new tools and fullstack services that work on all devices, and with the (added standardization) inclusion of MCP's there's now established pipelines for any kind of tools and/or services.\n\nI'm currently in the process of utilizing and leveraging these possibilities to simplify my own information feeds (such as gmail, youtube, instagram, facebook, etc) programmatically such that i can automate retrieval of things of interest, and remove things *not* of interest. My first stage in starting on this project is to initiate a simple testcase to establish a baseline that shows proof of concept and that will reveal each component in the pipeline-and the *pipeline* (itself and representations of it) will eventually be the single (and most abstract) source of truth.\n\nIn aiming for a good usecase that will help me pinpoint my needs and serve as a structure i can generalize and scaffold upon, i've decided the testcase to automation of the retrieval and filtering of youtube channels i subscribe to. This is a powerful usecase because i'll actually solve a real problem for myself; as i subscribe to more than a thousand creators and the youtube app doesn't offer any way of structuring (or automatically filter or setting up rules) to control my own feed. The effect of this/these uncontrolled (and continually growing) streams of information is that they've become so *vast* that the only way for me to draw value from it is to control it-because i can't decide upon something i never see; which i currently can't because without the ability for automation and filtering of the feed it's simply *too much information*.'



// 3031
// 3030
// 3022
// 3021
// 3017
// 3025
// 3003
// 3003
// 3005
// 3000
// 3020
// 3015
// 3016
// 3037
// 3100
// 3014
// 1900
// 3001
// 3036
