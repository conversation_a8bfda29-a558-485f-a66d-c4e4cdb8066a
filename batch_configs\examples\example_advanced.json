{"name": "v01.advanced_example", "description": "Advanced batch execution with complex sequences and multiple models", "config": {"chain_mode": true, "show_inputs": true, "show_system_instructions": true, "show_responses": true, "minified_output": false, "concurrent": true, "max_concurrent": 2}, "defaults": {"prompt": "Perform comprehensive analysis with multiple perspectives", "models": ["gpt-4o", "claude-3-sonnet"]}, "jobs": {"comprehensive-analysis": {"sequence": "1000|1400|1450", "output_prefix": "comprehensive"}, "deep-dive": {"sequence": "1700|1800", "models": ["gpt-5-chat-latest"], "output_prefix": "deep_dive"}, "multi-model-test": {"sequence": "1000", "models": ["gpt-4o", "claude-3-sonnet", "gemini-pro"], "output_prefix": "multi_model"}}}