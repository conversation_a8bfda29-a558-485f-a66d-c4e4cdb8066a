# Batch Execution Configuration

This directory contains batch execution configuration files for the AI Systems batch executor.

## Configuration Format

The new simplified batch configuration format uses an object-based structure with three main sections:

### 1. Basic Structure
```json
{
  "name": "v01.batch_name",
  "description": "Batch description",
  "config": { ... },
  "defaults": { ... },
  "jobs": { ... }
}
```

### 2. Configuration Sections

#### `config` Section
Global execution settings that apply to all jobs. These settings are merged into both the `defaults` section and the top level:
- `chain_mode`: Enable/disable chain mode (default: true)
- `show_inputs`: Show input prompts (default: true)
- `show_system_instructions`: Show system instructions (default: true)
- `show_responses`: Show model responses (default: true)
- `minified_output`: Use minified output format (default: false)
- `concurrent`: Run jobs concurrently (default: true)
- `max_concurrent`: Maximum concurrent jobs (optional)

**Important**: The `config` section values are automatically merged into the `defaults` section, so they become the default values for all jobs. Execution control fields like `concurrent` and `max_concurrent` are also merged to the top level for proper batch execution control.

#### `defaults` Section
Default parameters for all jobs:
- `prompt`: Default prompt text
- `models`: Default model list
- `chain_mode`: Default chain mode setting
- `show_inputs`: Default input display setting
- `show_system_instructions`: Default system instruction display
- `show_responses`: Default response display setting
- `minified_output`: Default output format setting
- `temperature`: Default temperature setting
- `max_tokens`: Default max tokens setting

#### `jobs` Section
Object where keys are job names and values are job configurations:
```json
"jobs": {
  "job-1": {
    "sequence": "1000",
    "models": ["gpt-4o"],
    "prompt": "Custom prompt for this job"
  },
  "job-2": {
    "sequence": "1000|1400|1450"
  }
}
```

### 3. Job Parameters

Each job can specify:
- `sequence`: Sequence specification (required)
- `models`: Model list (overrides defaults)
- `prompt`: Custom prompt (overrides defaults)
- `output_prefix`: Custom output filename prefix
- `chain_mode`: Chain mode setting (overrides defaults)
- `show_inputs`: Input display setting (overrides defaults)
- `show_system_instructions`: System instruction display (overrides defaults)
- `show_responses`: Response display setting (overrides defaults)
- `minified_output`: Output format setting (overrides defaults)
- `temperature`: Model temperature (overrides defaults)
- `max_tokens`: Max tokens (overrides defaults)

### 4. Priority Order

Parameters are resolved in this order (highest to lowest priority):
1. Job-specific settings
2. Defaults section (including merged config values)
3. Global execution defaults

### 5. Config Merging Behavior

The `config` section is automatically processed during configuration loading:

1. **Display Settings**: All display settings from `config` are merged into `defaults`
2. **Execution Control**: `concurrent` and `max_concurrent` are merged to both `defaults` and top level
3. **Job Inheritance**: Each job automatically inherits from the merged `defaults`

This ensures that:
- Display settings like `show_system_instructions: false` are properly applied to all jobs
- Execution control settings are accessible at the batch level
- Jobs can still override any setting individually

## Examples

### Basic Example (`example_basic.json`)
Simple batch with three analysis jobs using the same prompt and model.

### Sequential Example (`example_sequential.json`)
Sequential execution (non-concurrent) for resource-limited environments.

### Advanced Example (`example_advanced.json`)
Complex batch with multiple models, custom output prefixes, and concurrency limits.

### Tail Rhyme Executions (`tail_rhyme_executions.json`)
Real-world example with multiple sequence variations.

### Test Display Settings (`test_display_settings.json`)
Test configuration to verify that display settings are properly applied.

## Output Structure

The enhanced batch executor creates a hierarchical output structure:

```
└── output
    └── v01.batch_name                    # Batch root directory
        ├── batch_results--timestamp.json  # Batch execution summary
        └── gpt-4o                        # Model-specific subdirectory
            ├── batch--job-1--timestamp--1000.json
            ├── batch--job-2--timestamp--1000+1400.json
            └── batch--job-3--timestamp--1000+1400+1450.json
```

### Output Filename Pattern
Each job generates an output file with the naming pattern:
- `{output_prefix}--{job_name}--{timestamp}--{sequence_suffix}.json` (if output_prefix specified)
- `batch--{job_name}--{timestamp}--{sequence_suffix}.json` (default)

Where:
- `job_name`: The key from the jobs object
- `timestamp`: Execution timestamp in format `YYYY.MM.DD-kl.HH.MM.SS`
- `sequence_suffix`: Sequence specification with `|` replaced by `+`

### Centralized Filename Patterns

All filename patterns are now centralized in `src/config.py` under `FILENAME_PATTERNS`:

```python
FILENAME_PATTERNS = {
    "history": "history--{timestamp}--{sequence_id}--{model_tag}.json",
    "batch": "batch--{job_name}--{timestamp}--{sequence_suffix}.json",
    "batch_with_prefix": "{output_prefix}--{job_name}--{timestamp}--{sequence_suffix}.json",
    "batch_results": "batch_results--{timestamp}.json",
    "timestamp_format": "%Y.%m.%d-kl.%H.%M.%S",
    "timestamp_format_short": "%Y.%m.%d-kl.%H.%M",
    "timestamp_format_human": "%Y-%m-%d %H:%M:%S"
}
```

This ensures:
- **Consistency**: All timestamp formats across the system use the same patterns
- **Maintainability**: Changes to filename patterns only need to be made in one place
- **Flexibility**: Different timestamp formats for different use cases (filenames, human display, etc.)
- **Standardization**: Batch execution, main execution, and interactive mode all use the same naming conventions

## Benefits of New Format

1. **Simplified Configuration**: Jobs are defined as key-value pairs instead of arrays
2. **Inheritance**: Jobs automatically inherit from defaults and config sections
3. **Override Capability**: Individual jobs can override any setting
4. **Cleaner Structure**: Eliminates repetitive configuration in each job
5. **Better Organization**: Hierarchical output structure with batch-specific directories
6. **Sequence Information**: Sequence details are preserved in output filenames
7. **Proper Display Control**: Display settings from config section are correctly applied
8. **Centralized Filename Patterns**: All naming conventions are defined in one place for consistency and maintainability

## Migration from Old Format

The old array-based format:
```json
"jobs": [
  {
    "name": "job-1",
    "sequence": "1000",
    "models": ["gpt-4o"]
  }
]
```

Is now simplified to:
```json
"jobs": {
  "job-1": {
    "sequence": "1000",
    "models": ["gpt-4o"]
  }
}
```

## Usage

> Note on sequence syntax: You can use either ':' or '-' between the numeric ID and the step filter in specs. Examples: '3003:a', '3003-a', '3003:a-c', '3003-a-c'. Pipe-separated forms work with both: '3003:a|3100:d' or '3003-a|3100-d'.


Run a batch configuration:
```bash
python src/main.py --batch-file batch_configs/example_basic.json
```

Or directly with the batch executor:
```bash
python src/batch_executor.py batch_configs/example_basic.json
```

## Testing Display Settings

To verify that display settings are properly applied, use the test configuration:
```bash
python src/main.py --batch-file batch_configs/test_display_settings.json
```

This will run with:
- `show_system_instructions: false` (no system instructions displayed)
- `show_inputs: false` (no input prompts displayed)
- `show_responses: true` (responses are displayed)
- `minified_output: true` (minified output format)
- `concurrent: false` (sequential execution)
