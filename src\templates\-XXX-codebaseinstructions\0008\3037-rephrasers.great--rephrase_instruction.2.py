#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "3037-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
        "context": {
            "note": "Always Use PowerShell over cmd.exe on Windows system.",
            "description": "This is a hybridized standards specification document: fundamentally, it is a domain-agnostic software-engineering policy and code-design protocol. Its structure resembles a style guide, system-level engineering doctrine, or compliance blueprint— directly analogous to configuration manifests. The shortcut for maximum cross-domain leverage is to abstract this entire corpus into an external, machine-readable policy- engine interface layered with mapping adapters for common devtools (linters, code- review bots, project-scaffolding generators). This trivializes enforcement, enables plug-and-play reuse across language ecosystems, and renders most manual documentation processes obsolete—unifying codebase standards, review/check automation, and onboarding aids through a single, extensible governance API. This abstraction is both universally scalable and minimizes overhead since it aligns with existing policy-enforcement and manifest-driven CI/CD ecosystems.",
            "important": "To ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework— facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply- focused summary that details scope and explicit objectives.",
            "requirements": "- Always clean up after yourself, properly adapting to the existing structure of the codebase.\n- Before creating new verification tests, check the codebase for existing tests.\n- Write self-explanatory, well-structured code; use concise comments only where essential.\n- Justify any new files and ensure all additions align with the existing project organization.\n- Review recent code changes to guarantee comprehensive cleanup after edits.\n- Maintain meticulous conformity for seamless, harmonious integration.\n- Consider both construction details and the design rationale.\n- Preserve existing functionality, maximize clarity, and stay aligned before changing anything.",
            "core_principles": "- Derive patterns from clarity, structure, simplicity, elegance, precision, and intent.\n- Maintain inherent simplicity while delivering powerful functionality.\n- Embrace the truth that simplicity conquers chaos, pursuing high-impact, minimal-disruption innovations.\n- Seek universally resonant breakthroughs that marry contextual integrity with elite execution.",
            "general_principles": "- Aim for simplicity, clarity, and maintainability in all project aspects.\n- Favor composition over inheritance when applicable.\n- Prioritize readability and understandability for future developers.\n- Ensure every component has a single responsibility.\n- Adhere to coding standards that promote simplicity and maintainability.\n- Document only integral decisions in highly condensed form.",
            "code_organization": "- Evaluate the existing structure, noting patterns and anti-patterns.\n- Consolidate related functionality into cohesive modules.\n- Minimize dependencies between unrelated components.\n- Optimize for developer ergonomics and intuitive navigation.\n- Balance file granularity with overall system comprehensibility.\n- Optimize the directory structure for clarity, brevity, and broad applicability.\n- Verify that modifications align with project standards and avoid anomalies or duplication.",
        }
    },

    "3037-b-prompt_enhancer": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
