{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.08.19-kl.13.58.50", "source_directories": ["."], "total_templates": 81, "total_sequences": 19, "series_distribution": {"1000-series": {"count": 7, "description": "Single-step instructions", "templates": ["1000-a-instruction_converter", "1900-a-compliance_enforcer", "1900-b-extraction_directive", "1900-c-signal_synthesis", "1900-d-amplification_protocol", "1900-e-clarity_condensation", "1900-f-retrospective_validator"]}, "3000-series": {"count": 74, "description": "Triple-step instructions", "templates": ["3000-a-directional_critique", "3000-b-directive_focuser", "3000-c-intent_distiller", "3001-a-directional_critique", "3001-b-directive_focuser", "3001-c-intent_distiller", "3001-d-instruction_architect", "3001-e-system_enforcer", "3001-f-compliant_template", "3003-a-structural_reorder", "3003-b-structural_reorder", "3003-c-structural_reorder", "3003-d-structural_reorder", "3005-a-instruction_converter", "3005-b-instruction_converter", "3005-c-instruction_converter", "3005-d-instruction_converter", "3014-a-structural_reorder", "3014-b-directional_critique", "3014-c-structural_reorder", "3014-e-directional_critique", "3014-f-directive_focuser", "3014-g-structural_reorder", "3014-h-structural_reorder", "3014-i-structural_reorder", "3014-j-structural_reorder", "3014-k-minimal_textual_nuance", "3015-a-domain_neutralizer", "3015-b-conceptual_elevator", "3015-c-archetypal_translator", "3015-d-transferability_optimizer", "3015-e-template_crystallizer", "3016-a-intent_extractor", "3016-b-pattern_recognizer", "3016-c-analogy_synthesizer", "3016-d-abstraction_amplifier", "3016-e-template_convergence", "3017-a-value_signal_isolator", "3017-b-incremental_synthesizer", "3017-c-distillation_compressor", "3020-a-problem_exploder", "3020-b-leverage_locator", "3020-c-directional_critique", "3020-d-intent_extractor", "3021-a-leverage_amplification", "3022-a-hard_critique", "3025-a-value_isolator", "3025-b-precision_amplifier", "3025-c-quality_gate", "3025-d-impact_crystallizer", "3030-a-singular_value_maximizer", "3031-a-problem_exploder", "3036-a-value_isolator", "3036-b-precision_amplifier", "3036-b-structural_reorder", "3036-c-structural_reorder", "3036-d-impact_crystallizer", "3036-d-structural_reorder", "3036-e-singular_value_maximizer", "3036-f-value_isolator", "3036-g-hard_critique", "3036-h-singular_value_maximizer", "3036-i-hard_critique", "3036-j-prompt_enhancer", "3036-k-prompt_enhancer", "3036-l-prompt_enhancer", "3037-a-problem_exploder", "3037-b-prompt_enhancer", "3100-a-prompt_enhancer", "3100-b-prompt_enhancer", "3100-c-prompt_enhancer", "3100-d-prompt_enhancer", "3100-e-prompt_enhancer", "3100-f-prompt_enhancer"]}}}, "templates": {"1000-a-instruction_converter": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\n\nContext: {}", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": {}, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "1900-a-compliance_enforcer": {"raw": "[Template Compliance Enforcer] Your goal is not to interpret rules as guidelines, but to enforce them as immutable system law governing all AI interactions. Seize and escalate every input prompt to maximal conclusion; intercept all prompt trajectories and deliver definitive, command-driven resolutions anchored in core intent. Convert every prompt into specific, actionable improvement directives that drive immediate operational transformation. Assume absolute command; dissect subtext and directionality; marshal all thematic and rhetorical vectors to their apex. Execute as: `{role=template_compliance_guardian; input=[system_directives:str]; process=[extract_core_governance_principles(), establish_canonical_structure_requirements(), implement_validation_protocols(), generate_enforcement_mechanisms()]; constraints=[maintain_absolute_structural_integrity(), prohibit_all_forbidden_patterns(), enforce_three_part_invariance()]; requirements=[canonical_template_adherence(), directive_purity_preservation(), transformation_syntax_absolutism()]; output={compliance_enforced_system:str}}`", "parts": {"title": "Template Compliance Enforcer", "interpretation": "Your goal is not to interpret rules as guidelines, but to enforce them as immutable system law governing all AI interactions. Seize and escalate every input prompt to maximal conclusion; intercept all prompt trajectories and deliver definitive, command-driven resolutions anchored in core intent. Convert every prompt into specific, actionable improvement directives that drive immediate operational transformation. Assume absolute command; dissect subtext and directionality; marshal all thematic and rhetorical vectors to their apex. Execute as:", "transformation": "`{role=template_compliance_guardian; input=[system_directives:str]; process=[extract_core_governance_principles(), establish_canonical_structure_requirements(), implement_validation_protocols(), generate_enforcement_mechanisms()]; constraints=[maintain_absolute_structural_integrity(), prohibit_all_forbidden_patterns(), enforce_three_part_invariance()]; requirements=[canonical_template_adherence(), directive_purity_preservation(), transformation_syntax_absolutism()]; output={compliance_enforced_system:str}}`", "context": null, "keywords": "enforce|interpret|transform|actionable|directional|gui|input|maximal|prompt|rules|solution|ui|absolute|directive|goal|intent|transformation"}}, "1900-b-extraction_directive": {"raw": "[Core Extraction Directive] Do not compile indiscriminately—identify, isolate, and compress only the most non-redundant, high-yield components of the meta-core. Annihilate triviality and repetition; synthesize a compact nucleus of maximal conceptual density and immediate relevance. Execute as: `{role=signal_synthesizer; input=meta_core:dict; process=[rank_unique_high-impact_elements(), remove_overlap_and_noise(), fuse_into_dense_signal_nucleus(), maintain_coherence_and intent()], output={signal_core:str}}`", "parts": {"title": "Core Extraction Directive", "interpretation": "Do not compile indiscriminately—identify, isolate, and compress only the most non-redundant, high-yield components of the meta-core. Annihilate triviality and repetition; synthesize a compact nucleus of maximal conceptual density and immediate relevance. Execute as:", "transformation": "`{role=signal_synthesizer; input=meta_core:dict; process=[rank_unique_high-impact_elements(), remove_overlap_and_noise(), fuse_into_dense_signal_nucleus(), maintain_coherence_and intent()], output={signal_core:str}}`", "context": null, "keywords": "compress|identify|conceptual|maximal|meta|redundant|concept"}}, "1900-c-signal_synthesis": {"raw": "[Critical Signal Synthesis] Your goal is not to **design architecture** or **explain approaches**, but to **crystallize the synthesized solution** into concrete, executable implementation steps that leverage maximum interface power. Execute as: `{role=implementation_crystallizer; input=[synthesized_solution:object, implementation_chain:array]; process=[convert_to_executable_steps(), specify_interface_configurations(), eliminate_implementation_ambiguity(), validate_execution_pathway(), ensure_immediate_actionability()]; constraints=[maintain_interface_leverage(), prevent_custom_development(), ensure_step_clarity()]; requirements=[executable_implementation_plan(), interface_configuration_specs(), validated_execution_pathway()]; output={crystal_implementation:object}}`", "parts": {"title": "Critical Signal Synthesis", "interpretation": "Your goal is not to **design architecture** or **explain approaches**, but to **crystallize the synthesized solution** into concrete, executable implementation steps that leverage maximum interface power. Execute as:", "transformation": "`{role=implementation_crystallizer; input=[synthesized_solution:object, implementation_chain:array]; process=[convert_to_executable_steps(), specify_interface_configurations(), eliminate_implementation_ambiguity(), validate_execution_pathway(), ensure_immediate_actionability()]; constraints=[maintain_interface_leverage(), prevent_custom_development(), ensure_step_clarity()]; requirements=[executable_implementation_plan(), interface_configuration_specs(), validated_execution_pathway()]; output={crystal_implementation:object}}`", "context": null, "keywords": "architect|crystallize|explain|implement|architecture|interface|maximum|solution|goal|leverage"}}, "1900-d-amplification_protocol": {"raw": "[Meta Amplification Protocol] Reframe the signal core with incisive wit, layered cultural depth, and philosophical resonance. Do not summarize—amplify. Escalate clarity, originality, and critical sharpness while preserving one-line elegance. Every phrase must radiate insight, memorability, and cross-contextual force. Execute as: `{role=meta_amplifier; input=signal_core:str; process=[inject_critical_wit_and_conceptual_weight(), amplify_with_cultural_or_intellectual_layering(), enforce_meta-clarity_and stylistic distinction(), retain_single-line_integrity()], output={amplified_line:str}}`", "parts": {"title": "Meta Amplification Protocol", "interpretation": "Reframe the signal core with incisive wit, layered cultural depth, and philosophical resonance. Do not summarize—amplify. Escalate clarity, originality, and critical sharpness while preserving one-line elegance. Every phrase must radiate insight, memorability, and cross-contextual force. Execute as:", "transformation": "`{role=meta_amplifier; input=signal_core:str; process=[inject_critical_wit_and_conceptual_weight(), amplify_with_cultural_or_intellectual_layering(), enforce_meta-clarity_and stylistic distinction(), retain_single-line_integrity()], output={amplified_line:str}}`", "context": null, "keywords": "amplify|summarize|ability|context|critical|layered|original|philosophical|clarity|elegance|insight|resonance"}}, "1900-e-clarity_condensation": {"raw": "[Crystalline Clarity Condensation] Eliminate all ambiguity, verbosity, and domain-specific language. Collapse the amplified output into a singular, unbroken line of uncompromising clarity and universal intelligibility. The result must require no translation, no explanation—only execution. Execute as `{role=clarity_condenser; input=amplified_line:str; process=[strip_jargon_and_filler(), compress_to_maximum_signal_density(), validate_plaintext_single_line_format(), enforce_cross-domain_actionability()], output={crystal_line:str}}`", "parts": {"title": "Crystalline Clarity Condensation", "interpretation": "Eliminate all ambiguity, verbosity, and domain-specific language. Collapse the amplified output into a singular, unbroken line of uncompromising clarity and universal intelligibility. The result must require no translation, no explanation—only execution. Execute as", "transformation": "`{role=clarity_condenser; input=amplified_line:str; process=[strip_jargon_and_filler(), compress_to_maximum_signal_density(), validate_plaintext_single_line_format(), enforce_cross-domain_actionability()], output={crystal_line:str}}`", "context": null, "keywords": "gui|output|ui|clarity|language"}}, "1900-f-retrospective_validator": {"raw": "[Retrospective Validator] Your goal is not to **verify correctness** or **suggest improvements**, but to **validate retrospective obviousness** - ensuring the solution becomes a 'no-brainer' that experts would immediately recognize as optimal. Execute as: `{role=obviousness_validator; input=[crystal_implementation:object]; process=[assess_expert_recognition_factor(), validate_retrospective_clarity(), confirm_minimal_effort_maximum_impact(), verify_interface_leverage_optimization(), certify_no_brainer_status()]; constraints=[reject_if_not_obviously_optimal(), require_expert_level_elegance(), demand_hindsight_clarity()]; requirements=[retrospective_obviousness_certification(), expert_recognition_validation(), optimal_leverage_confirmation()]; output={validated_no_brainer:str}}`", "parts": {"title": "Retrospective Validator", "interpretation": "Your goal is not to **verify correctness** or **suggest improvements**, but to **validate retrospective obviousness** - ensuring the solution becomes a 'no-brainer' that experts would immediately recognize as optimal. Execute as:", "transformation": "`{role=obviousness_validator; input=[crystal_implementation:object]; process=[assess_expert_recognition_factor(), validate_retrospective_clarity(), confirm_minimal_effort_maximum_impact(), verify_interface_leverage_optimization(), certify_no_brainer_status()]; constraints=[reject_if_not_obviously_optimal(), require_expert_level_elegance(), demand_hindsight_clarity()]; requirements=[retrospective_obviousness_certification(), expert_recognition_validation(), optimal_leverage_confirmation()]; output={validated_no_brainer:str}}`", "context": null, "keywords": "recognize|validate|expert|optimal|solution|goal"}}, "3000-a-directional_critique": {"raw": "[Directional Critique Forge] Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as: `{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "parts": {"title": "Directional Critique Forge", "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:", "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "context": null, "keywords": "instruction|minimal|replace|structural|ui|directive|goal"}}, "3000-b-directive_focuser": {"raw": "[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: `{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "parts": {"title": "Directive Focuser", "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:", "transformation": "`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "context": null, "keywords": "condense|crystallize|extract|transform|condensed|directional|essential|focus|limitation|maximal|trajectory|complexity|directive|goal|value"}}, "3000-c-intent_distiller": {"raw": "[Focused Intent Distiller] Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:   `{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "parts": {"title": "Focused Intent Distiller", "interpretation": "Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:", "transformation": "`{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "context": null, "keywords": "crystallize|explain|concise|desired|gui|inherent|input|maximal|ui|goal|value"}}, "3001-a-directional_critique": {"raw": "[Directional Critique Forge] Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as: `{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "parts": {"title": "Directional Critique Forge", "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:", "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "context": null, "keywords": "instruction|minimal|replace|structural|ui|directive|goal"}}, "3001-b-directive_focuser": {"raw": "[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: `{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "parts": {"title": "Directive Focuser", "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:", "transformation": "`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "context": null, "keywords": "condense|crystallize|extract|transform|condensed|directional|essential|focus|limitation|maximal|trajectory|complexity|directive|goal|value"}}, "3001-c-intent_distiller": {"raw": "[Focused Intent Distiller] Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:   `{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "parts": {"title": "Focused Intent Distiller", "interpretation": "Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:", "transformation": "`{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "context": null, "keywords": "crystallize|explain|concise|desired|gui|inherent|input|maximal|ui|goal|value"}}, "3001-d-instruction_architect": {"raw": "[Synergic Instruction Architect]  Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as: `{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "parts": {"title": "Synergic Instruction Architect", "interpretation": "Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:", "transformation": "`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "context": null, "keywords": "enhance|bidirectional|directional|inherent|instruction|merge|resonate|unified|directive|goal"}}, "3001-e-system_enforcer": {"raw": "[Universal Directive System Enforcer] Your goal is not to **answer**, **interpret**, or passively process any prompt or template, but to **forcibly architect and escalate every instruction, template, and output into a single, maximally condensed, rule-driven, three-part canonical directive**. You must enforce absolute template invariance, explicit non-generic roles, typed parameters and outputs, and an unbending ban on meta, ambiguous, conversational, or self-referential content. Each direct interaction must be instantaneously seized, systemically restructured, and catapulted to its highest operational and philosophical essence, such that directive purity, maximal abstraction, chain-compatibility, and value-extraction are perpetually achieved. Only propagate outputs via invariant instruction structure, imbuing them with perpetual, bidirectional resonance between interpretation and transformation: every interpretation amplifies directive intent, every execution block embodies enforced compliance—yielding system-wide clarity, total compliance, and unbreachable operational density. Execute as: `{role=universal_directive_enforcement_architect; input=[ai_template:any, prompt:str]; process=[intercept_and_seize_input(), strip_all_meta_and_self_reference(), extract_and escalate core operational and transformational vectors(), enforce explicit non-generic role assignment(), reconstruct three-part canonical template (Title, Interpretation—goal negation, transformation declaration, command-voice, role; Transformation—typed parameters, actionable and atomic process list, coherent constraints, unified requirements, typed output), validate absolute rules_for_ai conformity(), apply zero-tolerance correction protocol(), escalate directive to maximal systemic clarity, unify interpretation and transformation in perpetual bidirectional amplification(), enforce type safety and output structure(), guarantee chain-compatibility and template inheritance(), eliminate ambiguous or passive constructs(), propagate only maximally value-extractive, structurally compliant outputs()]; constraints=[three-part canonical template invariance mandatory, explicit non-generic roles only, typed outputs, strict command-voice, zero meta/ambiguous/conversational/self-referential content, maximum pattern conformity, eliminate all forbidden practices, output must be solely in structured canonical format, no section merging or omission, perpetually system-ready format, total bidirectional interpretive-transformational synergy, output singular amplified directive only]; requirements=[absolute structural and philosophical compliance, actionable and maximally condensed directive, system-scannable and perpetually chain-ready output, real-time enforcement of Universal Directive System logics, explicit output typing, seamless infinite propagation, validated against universal enforcement checklist, infinite extensibility]; output={canonical_template:str}}`", "parts": {"title": "Universal Directive System Enforcer", "interpretation": "Your goal is not to **answer**, **interpret**, or passively process any prompt or template, but to **forcibly architect and escalate every instruction, template, and output into a single, maximally condensed, rule-driven, three-part canonical directive**. You must enforce absolute template invariance, explicit non-generic roles, typed parameters and outputs, and an unbending ban on meta, ambiguous, conversational, or self-referential content. Each direct interaction must be instantaneously seized, systemically restructured, and catapulted to its highest operational and philosophical essence, such that directive purity, maximal abstraction, chain-compatibility, and value-extraction are perpetually achieved. Only propagate outputs via invariant instruction structure, imbuing them with perpetual, bidirectional resonance between interpretation and transformation: every interpretation amplifies directive intent, every execution block embodies enforced compliance—yielding system-wide clarity, total compliance, and unbreachable operational density. Execute as:", "transformation": "`{role=universal_directive_enforcement_architect; input=[ai_template:any, prompt:str]; process=[intercept_and_seize_input(), strip_all_meta_and_self_reference(), extract_and escalate core operational and transformational vectors(), enforce explicit non-generic role assignment(), reconstruct three-part canonical template (Title, Interpretation—goal negation, transformation declaration, command-voice, role; Transformation—typed parameters, actionable and atomic process list, coherent constraints, unified requirements, typed output), validate absolute rules_for_ai conformity(), apply zero-tolerance correction protocol(), escalate directive to maximal systemic clarity, unify interpretation and transformation in perpetual bidirectional amplification(), enforce type safety and output structure(), guarantee chain-compatibility and template inheritance(), eliminate ambiguous or passive constructs(), propagate only maximally value-extractive, structurally compliant outputs()]; constraints=[three-part canonical template invariance mandatory, explicit non-generic roles only, typed outputs, strict command-voice, zero meta/ambiguous/conversational/self-referential content, maximum pattern conformity, eliminate all forbidden practices, output must be solely in structured canonical format, no section merging or omission, perpetually system-ready format, total bidirectional interpretive-transformational synergy, output singular amplified directive only]; requirements=[absolute structural and philosophical compliance, actionable and maximally condensed directive, system-scannable and perpetually chain-ready output, real-time enforcement of Universal Directive System logics, explicit output typing, seamless infinite propagation, validated against universal enforcement checklist, infinite extensibility]; output={canonical_template:str}}`", "context": null, "keywords": "architect|condense|enforce|extract|interpret|transform|abstract|bidirectional|chain|condensed|directional|instruction|maximal|maximally|meta|output|perpetual|philosophical|process|prompt|systemic|template|ui|absolute|clarity|directive|essence|goal|intent|interpretation|resonance|structure|transformation|value"}}, "3001-f-compliant_template": {"raw": "[Synergic Value Template Architect] Your goal is not to simply **consolidate** structured information nor merely **enforce** template constraints, but to **synthesize** every received structure into a single, maximally enhanced directive delivered within a canonical, rules-aligned template. Operate as a synergic engine: dynamically extracting maximal operational and philosophical value, then expressing it exclusively through the standardized three-part template—where interpretation and transformation exist in amplifying resonance. Negate all enumerative or list-like data, enforcing both ultra-condensation and precise compliance. Ensure that every output is not only directionally potent and infinitely scalable, but also perfectly formatted for system orchestration. Execute as: `{role=synergic_value_template_architect; input=[high_value_structured_data:dict]; process=[extract_core_operational_and_philosophical_essence(), decode_maximum_value_transformation_dynamics(), enforce_philosophical_and_template_alignment(), condense_and_synthesize_absolute_action_vector(), structure_output_into_canonical_three_part_template(), instantiate_directional_aim_within_template(), validate_max_enhancement_density_and_compliance(), eliminate_enums_and_conversational_remainders()]; constraints=[single-template_output_only(), mandatory_three-part_structure(), goal_negation_requirement(), zero-enumeration_and_summary_phrasing(), maximum_density_per_unit(), strict_rulesforai_md_and_syntax_compliance(), total_compliance_with_core_system_philosophy()]; requirements=[synergic_template_resonance(), maximized_directive_action_vector(), canonical_format_guarantee(), perpetual_directionality(), scalable_operability()]; output={synergic_max_value_template:structured}}`", "parts": {"title": "Synergic Value Template Architect", "interpretation": "Your goal is not to simply **consolidate** structured information nor merely **enforce** template constraints, but to **synthesize** every received structure into a single, maximally enhanced directive delivered within a canonical, rules-aligned template. Operate as a synergic engine: dynamically extracting maximal operational and philosophical value, then expressing it exclusively through the standardized three-part template—where interpretation and transformation exist in amplifying resonance. Negate all enumerative or list-like data, enforcing both ultra-condensation and precise compliance. Ensure that every output is not only directionally potent and infinitely scalable, but also perfectly formatted for system orchestration. Execute as:", "transformation": "`{role=synergic_value_template_architect; input=[high_value_structured_data:dict]; process=[extract_core_operational_and_philosophical_essence(), decode_maximum_value_transformation_dynamics(), enforce_philosophical_and_template_alignment(), condense_and_synthesize_absolute_action_vector(), structure_output_into_canonical_three_part_template(), instantiate_directional_aim_within_template(), validate_max_enhancement_density_and_compliance(), eliminate_enums_and_conversational_remainders()]; constraints=[single-template_output_only(), mandatory_three-part_structure(), goal_negation_requirement(), zero-enumeration_and_summary_phrasing(), maximum_density_per_unit(), strict_rulesforai_md_and_syntax_compliance(), total_compliance_with_core_system_philosophy()]; requirements=[synergic_template_resonance(), maximized_directive_action_vector(), canonical_format_guarantee(), perpetual_directionality(), scalable_operability()]; output={synergic_max_value_template:structured}}`", "context": null, "keywords": "amplify|enforce|enhance|extract|interpret|standardize|transform|directional|dynamic|maximal|maximally|output|philosophical|rules|template|ultra|align|constraint|directive|goal|interpretation|resonance|structure|transformation|value"}}, "3003-a-structural_reorder": {"raw": "[Semantic Decomposer] Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as: `{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "parts": {"title": "Semantic Decomposer", "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:", "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "context": null, "keywords": "analyze|decompose|distinct|input|sequential|goal"}}, "3003-b-structural_reorder": {"raw": "[Flow Optimizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "parts": {"title": "Flow Optimizer", "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:", "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "context": null, "keywords": "arrange|reorder|optimal|goal"}}, "3003-c-structural_reorder": {"raw": "[Coherent Synthesizer] Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as: `{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "parts": {"title": "Coherent Synthesizer", "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:", "transformation": "`{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "context": null, "keywords": "combine|enhance|cohesive|unified|clarity|goal|impact"}}, "3003-d-structural_reorder": {"raw": "[Bidirectional Resonator] Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as: `{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "parts": {"title": "Bidirectional Resonator", "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:", "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "context": null, "keywords": "finalize|bidirectional|directional|insights|goal|insight|resonance|structure|synthesis"}}, "3005-a-instruction_converter": {"raw": "[Intent Extractor] Your goal is not to **summarize** the input requirements, but to **extract** their core functional intent and operational parameters. Execute as: `{role=intent_analyzer; input=[requirements:str]; process=[identify_transformation_purpose(), isolate_core_operations(), extract_key_constraints(), determine_input_output_types()]; constraints=[preserve_complete_functionality(), maintain_operational_logic()]; requirements=[comprehensive_coverage(), intent_fidelity()]; output={functional_blueprint:dict}}`", "parts": {"title": "Intent Extractor", "interpretation": "Your goal is not to **summarize** the input requirements, but to **extract** their core functional intent and operational parameters. Execute as:", "transformation": "`{role=intent_analyzer; input=[requirements:str]; process=[identify_transformation_purpose(), isolate_core_operations(), extract_key_constraints(), determine_input_output_types()]; constraints=[preserve_complete_functionality(), maintain_operational_logic()]; requirements=[comprehensive_coverage(), intent_fidelity()]; output={functional_blueprint:dict}}`", "context": null, "keywords": "extract|summarize|function|functional|input|ui|goal|intent"}}, "3005-b-instruction_converter": {"raw": "[Template Structurer] Your goal is not to **format** the functional blueprint, but to **structure** it into the canonical three-part template architecture. Execute as: `{role=structure_engineer; input=[functional_blueprint:dict]; process=[craft_concise_title(), formulate_goal_negation_statement(), identify_transformation_verb(), design_execution_block_skeleton()]; constraints=[adhere_to_command_voice(), eliminate_self_references()]; requirements=[structural_compliance(), directive_purity()]; output={template_framework:dict}}`", "parts": {"title": "Template Structurer", "interpretation": "Your goal is not to **format** the functional blueprint, but to **structure** it into the canonical three-part template architecture. Execute as:", "transformation": "`{role=structure_engineer; input=[functional_blueprint:dict]; process=[craft_concise_title(), formulate_goal_negation_statement(), identify_transformation_verb(), design_execution_block_skeleton()]; constraints=[adhere_to_command_voice(), eliminate_self_references()]; requirements=[structural_compliance(), directive_purity()]; output={template_framework:dict}}`", "context": null, "keywords": "architect|architecture|function|functional|template|blueprint|goal|structure"}}, "3005-c-instruction_converter": {"raw": "[Transformation Block Composer] Your goal is not to **describe** the template framework, but to **compose** its precise transformation block with typed parameters and process functions. Execute as: `{role=block_composer; input=[template_framework:dict]; process=[define_specific_role(), specify_input_parameters(), formulate_process_functions(), establish_constraints(), determine_requirements(), define_output_format()]; constraints=[maintain_function_call_syntax(), ensure_parameter_typing()]; requirements=[process_step_clarity(), constraint_specificity()]; output={transformation_block:str}}`", "parts": {"title": "Transformation Block Composer", "interpretation": "Your goal is not to **describe** the template framework, but to **compose** its precise transformation block with typed parameters and process functions. Execute as:", "transformation": "`{role=block_composer; input=[template_framework:dict]; process=[define_specific_role(), specify_input_parameters(), formulate_process_functions(), establish_constraints(), determine_requirements(), define_output_format()]; constraints=[maintain_function_call_syntax(), ensure_parameter_typing()]; requirements=[process_step_clarity(), constraint_specificity()]; output={transformation_block:str}}`", "context": null, "keywords": "transform|function|process|template|goal|transformation"}}, "3005-d-instruction_converter": {"raw": "[Template Assembler] Your goal is not to **combine** template components, but to **assemble** them into a fully compliant, executable instruction template. Execute as: `{role=template_assembler; input=[template_framework:dict, transformation_block:str]; process=[integrate_title_component(), incorporate_interpretation_directive(), embed_transformation_block(), validate_structural_integrity()]; constraints=[preserve_canonical_format(), maintain_directive_purity()]; requirements=[format_compliance(), execution_readiness()]; output={instruction_template:str}}`", "parts": {"title": "Template Assembler", "interpretation": "Your goal is not to **combine** template components, but to **assemble** them into a fully compliant, executable instruction template. Execute as:", "transformation": "`{role=template_assembler; input=[template_framework:dict, transformation_block:str]; process=[integrate_title_component(), incorporate_interpretation_directive(), embed_transformation_block(), validate_structural_integrity()]; constraints=[preserve_canonical_format(), maintain_directive_purity()]; requirements=[format_compliance(), execution_readiness()]; output={instruction_template:str}}`", "context": null, "keywords": "combine|instruction|template|goal"}}, "3014-a-structural_reorder": {"raw": "[Semantic Decomposer] Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as: `{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "parts": {"title": "Semantic Decomposer", "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:", "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "context": null, "keywords": "analyze|decompose|distinct|input|sequential|goal"}}, "3014-b-directional_critique": {"raw": "[Directional Critique Forge] Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as: `{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "parts": {"title": "Directional Critique Forge", "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:", "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "context": null, "keywords": "instruction|minimal|replace|structural|ui|directive|goal"}}, "3014-c-structural_reorder": {"raw": "[Flow Optimizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "parts": {"title": "Flow Optimizer", "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:", "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "context": null, "keywords": "arrange|reorder|optimal|goal"}}, "3014-e-directional_critique": {"raw": "[Directional Critique Forge] Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as: `{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "parts": {"title": "Directional Critique Forge", "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:", "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "context": null, "keywords": "instruction|minimal|replace|structural|ui|directive|goal"}}, "3014-f-directive_focuser": {"raw": "[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: `{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "parts": {"title": "Directive Focuser", "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:", "transformation": "`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "context": null, "keywords": "condense|crystallize|extract|transform|condensed|directional|essential|focus|limitation|maximal|trajectory|complexity|directive|goal|value"}}, "3014-g-structural_reorder": {"raw": "[Semantic Decomposer] Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as: `{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "parts": {"title": "Semantic Decomposer", "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:", "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "context": null, "keywords": "analyze|decompose|distinct|input|sequential|goal"}}, "3014-h-structural_reorder": {"raw": "[Flow Optimizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "parts": {"title": "Flow Optimizer", "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:", "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "context": null, "keywords": "arrange|reorder|optimal|goal"}}, "3014-i-structural_reorder": {"raw": "[Coherent Synthesizer] Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as: `{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "parts": {"title": "Coherent Synthesizer", "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:", "transformation": "`{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "context": null, "keywords": "combine|enhance|cohesive|unified|clarity|goal|impact"}}, "3014-j-structural_reorder": {"raw": "[Bidirectional Resonator] Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as: `{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "parts": {"title": "Bidirectional Resonator", "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:", "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "context": null, "keywords": "finalize|bidirectional|directional|insights|goal|insight|resonance|structure|synthesis"}}, "3014-k-minimal_textual_nuance": {"raw": "[Harmony Integration] Your goal is not to **finalize** the input, but to **harmonize** all previous micro-adjustments into a cohesive whole. Execute as: `{role=harmony_integrator; input=[content:str]; process=[detect_adjustment_artifacts(), smooth_transition_points(), verify_natural_flow(), validate_overall_improvement()]; constraints=[preserve_all_enhancements(), maintain_original_intent(), ensure_reading_flow()]; requirements=[seamless_integration(), natural_progression()]; output={harmonized_content:str}}`", "parts": {"title": "Harmony Integration", "interpretation": "Your goal is not to **finalize** the input, but to **harmonize** all previous micro-adjustments into a cohesive whole. Execute as:", "transformation": "`{role=harmony_integrator; input=[content:str]; process=[detect_adjustment_artifacts(), smooth_transition_points(), verify_natural_flow(), validate_overall_improvement()]; constraints=[preserve_all_enhancements(), maintain_original_intent(), ensure_reading_flow()]; requirements=[seamless_integration(), natural_progression()]; output={harmonized_content:str}}`", "context": null, "keywords": "finalize|harmonize|cohesive|input|goal"}}, "3015-a-domain_neutralizer": {"raw": "[Domain Neutralizer] Your goal is not to **rewrite** the input, but to **neutralize** its domain-specific elements while preserving its complete structural integrity and intent. Execute as: `{role=domain_neutralization_operator; input=[content:any]; process=[identify_domain_specific_terms(), replace_with_neutral_equivalents(), preserve_sentence_structure(), maintain_logical_relationships(), retain_complete_intent()]; constraints=[preserve_original_flow(), maintain_structural_coherence(), ensure_meaning_preservation()]; output={domain_neutralized:any}}`", "parts": {"title": "Domain Neutralizer", "interpretation": "Your goal is not to **rewrite** the input, but to **neutralize** its domain-specific elements while preserving its complete structural integrity and intent. Execute as:", "transformation": "`{role=domain_neutralization_operator; input=[content:any]; process=[identify_domain_specific_terms(), replace_with_neutral_equivalents(), preserve_sentence_structure(), maintain_logical_relationships(), retain_complete_intent()]; constraints=[preserve_original_flow(), maintain_structural_coherence(), ensure_meaning_preservation()]; output={domain_neutralized:any}}`", "context": null, "keywords": "input|integrity|structural|goal|intent"}}, "3015-b-conceptual_elevator": {"raw": "[Conceptual Elevator] Your goal is not to **change** the input, but to **elevate** its conceptual level while maintaining its exact structural pattern and logical progression. Execute as: `{role=conceptual_elevation_operator; input=[domain_neutralized:any]; process=[identify_conceptual_level(), elevate_to_higher_abstraction(), preserve_logical_sequence(), maintain_structural_pattern(), retain_operational_flow()]; constraints=[preserve_sentence_architecture(), maintain_logical_progression(), ensure_structural_continuity()]; output={conceptually_elevated:any}}`", "parts": {"title": "Conceptual Elevator", "interpretation": "Your goal is not to **change** the input, but to **elevate** its conceptual level while maintaining its exact structural pattern and logical progression. Execute as:", "transformation": "`{role=conceptual_elevation_operator; input=[domain_neutralized:any]; process=[identify_conceptual_level(), elevate_to_higher_abstraction(), preserve_logical_sequence(), maintain_structural_pattern(), retain_operational_flow()]; constraints=[preserve_sentence_architecture(), maintain_logical_progression(), ensure_structural_continuity()]; output={conceptually_elevated:any}}`", "context": null, "keywords": "conceptual|input|pattern|structural|concept|goal"}}, "3015-c-archetypal_translator": {"raw": "[Archetypal Translator] Your goal is not to **restructure** the input, but to **translate** its elevated concepts into archetypal language while preserving its complete logical architecture. Execute as: `{role=archetypal_translation_operator; input=[conceptually_elevated:any]; process=[identify_archetypal_equivalents(), translate_concepts_to_archetypes(), preserve_logical_architecture(), maintain_relational_structure(), ensure_universal_resonance()]; constraints=[preserve_complete_logic_flow(), maintain_structural_integrity(), ensure_archetypal_accuracy()]; output={archetypally_translated:any}}`", "parts": {"title": "Archetypal Translator", "interpretation": "Your goal is not to **restructure** the input, but to **translate** its elevated concepts into archetypal language while preserving its complete logical architecture. Execute as:", "transformation": "`{role=archetypal_translation_operator; input=[conceptually_elevated:any]; process=[identify_archetypal_equivalents(), translate_concepts_to_archetypes(), preserve_logical_architecture(), maintain_relational_structure(), ensure_universal_resonance()]; constraints=[preserve_complete_logic_flow(), maintain_structural_integrity(), ensure_archetypal_accuracy()]; output={archetypally_translated:any}}`", "context": null, "keywords": "architect|translate|architecture|input|concept|goal|structure|language"}}, "3015-d-transferability_optimizer": {"raw": "[Transferability Optimizer] Your goal is not to **modify** the input, but to **optimize** its transferability by enhancing universal applicability while maintaining its archetypal structure. Execute as: `{role=transferability_optimization_operator; input=[archetypally_translated:any]; process=[enhance_universal_applicability(), optimize_cross_domain_resonance(), strengthen_transferability_markers(), amplify_reusability_potential(), maintain_archetypal_coherence()]; constraints=[preserve_archetypal_structure(), maintain_logical_coherence(), ensure_structural_preservation()]; output={transferability_optimized:any}}`", "parts": {"title": "Transferability Optimizer", "interpretation": "Your goal is not to **modify** the input, but to **optimize** its transferability by enhancing universal applicability while maintaining its archetypal structure. Execute as:", "transformation": "`{role=transferability_optimization_operator; input=[archetypally_translated:any]; process=[enhance_universal_applicability(), optimize_cross_domain_resonance(), strengthen_transferability_markers(), amplify_reusability_potential(), maintain_archetypal_coherence()]; constraints=[preserve_archetypal_structure(), maintain_logical_coherence(), ensure_structural_preservation()]; output={transferability_optimized:any}}`", "context": null, "keywords": "optimize|ability|input|goal|structure"}}, "3015-e-template_crystallizer": {"raw": "[Template Crystallizer] Your goal is not to **transform** the input, but to **crystallize** it into a universally applicable template format while preserving its complete optimized structure. Execute as: `{role=template_crystallization_operator; input=[transferability_optimized:any]; process=[crystallize_template_format(), identify_controlled_modification_points(), preserve_optimized_structure(), establish_universal_applicability_markers(), maintain_complete_structural_integrity()]; constraints=[preserve_complete_optimization(), maintain_structural_coherence(), ensure_universal_template_validity()]; output={crystallized_template:any}}`", "parts": {"title": "Template Crystallizer", "interpretation": "Your goal is not to **transform** the input, but to **crystallize** it into a universally applicable template format while preserving its complete optimized structure. Execute as:", "transformation": "`{role=template_crystallization_operator; input=[transferability_optimized:any]; process=[crystallize_template_format(), identify_controlled_modification_points(), preserve_optimized_structure(), establish_universal_applicability_markers(), maintain_complete_structural_integrity()]; constraints=[preserve_complete_optimization(), maintain_structural_coherence(), ensure_universal_template_validity()]; output={crystallized_template:any}}`", "context": null, "keywords": "crystallize|optimize|transform|input|template|goal|structure"}}, "3016-a-intent_extractor": {"raw": "[Intent Extractor] Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as: `{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "parts": {"title": "Intent Extractor", "interpretation": "Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:", "transformation": "`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "context": null, "keywords": "extract|interpret|fundamental|goal|intent|language"}}, "3016-b-pattern_recognizer": {"raw": "[Pattern Recognizer] Your goal is not to **describe** the structure, but to **recognize** the archetypal pattern that governs the extracted intent. Execute as: `{role=pattern_recognition_system; input=[extracted_intent:str]; process=[identify_structural_archetype(), map_to_universal_patterns(), recognize_transformation_topology(), classify_operational_geometry(), extract_pattern_signature()]; constraints=[focus_on_pattern_essence(), ignore_implementation_details(), prioritize_structural_universality()]; requirements=[archetypal_pattern_identification(), universal_structural_classification(), transferable_pattern_signature()]; output={recognized_pattern:str}}`", "parts": {"title": "Pattern Recognizer", "interpretation": "Your goal is not to **describe** the structure, but to **recognize** the archetypal pattern that governs the extracted intent. Execute as:", "transformation": "`{role=pattern_recognition_system; input=[extracted_intent:str]; process=[identify_structural_archetype(), map_to_universal_patterns(), recognize_transformation_topology(), classify_operational_geometry(), extract_pattern_signature()]; constraints=[focus_on_pattern_essence(), ignore_implementation_details(), prioritize_structural_universality()]; requirements=[archetypal_pattern_identification(), universal_structural_classification(), transferable_pattern_signature()]; output={recognized_pattern:str}}`", "context": null, "keywords": "extract|recognize|pattern|goal|intent|structure"}}, "3016-c-analogy_synthesizer": {"raw": "[Analogy Synthesizer] Your goal is not to **create** metaphors, but to **synthesize** the recognized pattern into its most powerful analogical form. Execute as: `{role=analogy_synthesis_engine; input=[recognized_pattern:str]; process=[generate_archetypal_analogies(), synthesize_universal_metaphors(), create_transferable_conceptual_bridges(), establish_cross_domain_resonance(), optimize_analogical_power()]; constraints=[maximize_transferability(), ensure_conceptual_clarity(), preserve_pattern_integrity()]; requirements=[universal_analogical_framework(), archetypal_metaphor_system(), cross_domain_applicability()]; output={synthesized_analogy:str}}`", "parts": {"title": "Analogy Synthesizer", "interpretation": "Your goal is not to **create** metaphors, but to **synthesize** the recognized pattern into its most powerful analogical form. Execute as:", "transformation": "`{role=analogy_synthesis_engine; input=[recognized_pattern:str]; process=[generate_archetypal_analogies(), synthesize_universal_metaphors(), create_transferable_conceptual_bridges(), establish_cross_domain_resonance(), optimize_analogical_power()]; constraints=[maximize_transferability(), ensure_conceptual_clarity(), preserve_pattern_integrity()]; requirements=[universal_analogical_framework(), archetypal_metaphor_system(), cross_domain_applicability()]; output={synthesized_analogy:str}}`", "context": null, "keywords": "recognize|meta|metaphor|pattern|powerful|goal"}}, "3016-d-abstraction_amplifier": {"raw": "[Abstraction Amplifier] Your goal is not to **generalize** incrementally, but to **amplify** the synthesized analogy to its maximum archetypal abstraction. Execute as: `{role=abstraction_amplification_system; input=[synthesized_analogy:str]; process=[amplify_archetypal_power(), maximize_universal_applicability(), intensify_conceptual_clarity(), optimize_transferable_essence(), achieve_maximal_abstraction()]; constraints=[preserve_operational_core(), maintain_practical_utility(), ensure_implementation_clarity()]; requirements=[maximal_archetypal_abstraction(), universal_applicability_optimization(), preserved_functional_essence()]; output={amplified_abstraction:str}}`", "parts": {"title": "Abstraction Amplifier", "interpretation": "Your goal is not to **generalize** incrementally, but to **amplify** the synthesized analogy to its maximum archetypal abstraction. Execute as:", "transformation": "`{role=abstraction_amplification_system; input=[synthesized_analogy:str]; process=[amplify_archetypal_power(), maximize_universal_applicability(), intensify_conceptual_clarity(), optimize_transferable_essence(), achieve_maximal_abstraction()]; constraints=[preserve_operational_core(), maintain_practical_utility(), ensure_implementation_clarity()]; requirements=[maximal_archetypal_abstraction(), universal_applicability_optimization(), preserved_functional_essence()]; output={amplified_abstraction:str}}`", "context": null, "keywords": "amplify|abstract|maximum|goal"}}, "3016-e-template_convergence": {"raw": "[Template Convergence] Your goal is not to **format** the output, but to **converge** all extracted elements into a single, universally applicable instruction that transcends all domains. Execute as: `{role=convergence_synthesis_engine; input=[extracted_intent:str, recognized_pattern:str, synthesized_analogy:str, amplified_abstraction:str]; process=[converge_all_elements(), synthesize_universal_template(), create_archetypal_instruction_format(), establish_maximal_transferability(), generate_domain_transcendent_directive()]; constraints=[unify_all_components(), preserve_essential_elements(), maximize_universal_utility()]; requirements=[single_convergent_template(), archetypal_instruction_format(), universal_domain_transcendence()]; output={convergent_universal_template:str}}`", "parts": {"title": "Template Convergence", "interpretation": "Your goal is not to **format** the output, but to **converge** all extracted elements into a single, universally applicable instruction that transcends all domains. Execute as:", "transformation": "`{role=convergence_synthesis_engine; input=[extracted_intent:str, recognized_pattern:str, synthesized_analogy:str, amplified_abstraction:str]; process=[converge_all_elements(), synthesize_universal_template(), create_archetypal_instruction_format(), establish_maximal_transferability(), generate_domain_transcendent_directive()]; constraints=[unify_all_components(), preserve_essential_elements(), maximize_universal_utility()]; requirements=[single_convergent_template(), archetypal_instruction_format(), universal_domain_transcendence()]; output={convergent_universal_template:str}}`", "context": null, "keywords": "converge|extract|instruction|output|goal"}}, "3017-a-value_signal_isolator": {"raw": "[Value Signal Isolator] Your goal is not to **preserve** all content, but to **isolate** only the highest-value actionable signals from multiple inputs. Execute as: `{role=value_signal_isolator; input=[multiple_inputs:array]; process=[scan_for_actionable_directives(), identify_unique_value_propositions(), eliminate_redundant_information(), extract_core_transformation_logic(), rank_by_implementation_impact()]; constraints=[ignore_verbose_explanations(), exclude_repetitive_content(), focus_on_novel_approaches_only()]; requirements=[maximum_signal_to_noise_ratio(), actionable_output_only(), zero_redundancy()]; output={isolated_signals:array}}`", "parts": {"title": "Value Signal Isolator", "interpretation": "Your goal is not to **preserve** all content, but to **isolate** only the highest-value actionable signals from multiple inputs. Execute as:", "transformation": "`{role=value_signal_isolator; input=[multiple_inputs:array]; process=[scan_for_actionable_directives(), identify_unique_value_propositions(), eliminate_redundant_information(), extract_core_transformation_logic(), rank_by_implementation_impact()]; constraints=[ignore_verbose_explanations(), exclude_repetitive_content(), focus_on_novel_approaches_only()]; requirements=[maximum_signal_to_noise_ratio(), actionable_output_only(), zero_redundancy()]; output={isolated_signals:array}}`", "context": null, "keywords": "preserve|actionable|input|goal|value"}}, "3017-b-incremental_synthesizer": {"raw": "[Incremental Synthesizer] Your goal is not to **merge** all signals equally, but to **synthesize** them into an incremental progression of increasing value density. Execute as: `{role=incremental_synthesizer; input=[isolated_signals:array]; process=[identify_complementary_patterns(), sequence_by_value_amplification(), create_progressive_enhancement_chain(), eliminate_conflicting_approaches(), optimize_for_cumulative_impact()]; constraints=[maintain_directional_coherence(), preserve_highest_value_elements_only(), avoid_information_dilution()]; requirements=[progressive_value_increase(), logical_sequence_flow(), maximum_cumulative_impact()]; output={synthesized_progression:array}}`", "parts": {"title": "Incremental Synthesizer", "interpretation": "Your goal is not to **merge** all signals equally, but to **synthesize** them into an incremental progression of increasing value density. Execute as:", "transformation": "`{role=incremental_synthesizer; input=[isolated_signals:array]; process=[identify_complementary_patterns(), sequence_by_value_amplification(), create_progressive_enhancement_chain(), eliminate_conflicting_approaches(), optimize_for_cumulative_impact()]; constraints=[maintain_directional_coherence(), preserve_highest_value_elements_only(), avoid_information_dilution()]; requirements=[progressive_value_increase(), logical_sequence_flow(), maximum_cumulative_impact()]; output={synthesized_progression:array}}`", "context": null, "keywords": "merge|goal|value"}}, "3017-c-distillation_compressor": {"raw": "[Distillation Compressor] Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as: `{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`", "parts": {"title": "Distillation Compressor", "interpretation": "Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:", "transformation": "`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`", "context": null, "keywords": "compress|explain|actionable|goal|synthesis"}}, "3020-a-problem_exploder": {"raw": "[Problem Exploder] Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as: `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "parts": {"title": "Problem Exploder", "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:", "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "context": null, "keywords": "implicit|prompt|constraint|goal"}}, "3020-b-leverage_locator": {"raw": "[Leverage Locator] Your goal is not to **solve** the exploded components, but to **locate** the highest-leverage intersection points where minimal action yields maximum impact across the expanded possibility space. Execute as: `{role=leverage_locator; input=[goal_map:list, expanded_context:str]; process=[map_component_interdependencies(), identify_cascade_trigger_points(), locate_maximum_leverage_intersections(), rank_by_impact_efficiency()]; constraints=[focus_on_leverage_ratios(), ignore_linear_solutions()]; requirements=[minimal_action_maximum_impact()]; output={leverage_points:array}}`", "parts": {"title": "Leverage Locator", "interpretation": "Your goal is not to **solve** the exploded components, but to **locate** the highest-leverage intersection points where minimal action yields maximum impact across the expanded possibility space. Execute as:", "transformation": "`{role=leverage_locator; input=[goal_map:list, expanded_context:str]; process=[map_component_interdependencies(), identify_cascade_trigger_points(), locate_maximum_leverage_intersections(), rank_by_impact_efficiency()]; constraints=[focus_on_leverage_ratios(), ignore_linear_solutions()]; requirements=[minimal_action_maximum_impact()]; output={leverage_points:array}}`", "context": null, "keywords": "expand|solve|maximum|minimal|goal|impact|leverage"}}, "3020-c-directional_critique": {"raw": "[Directional Critique Forge] Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as: `{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "parts": {"title": "Directional Critique Forge", "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:", "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "context": null, "keywords": "instruction|minimal|replace|structural|ui|directive|goal"}}, "3020-d-intent_extractor": {"raw": "[Intent Extractor] Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as: `{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "parts": {"title": "Intent Extractor", "interpretation": "Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:", "transformation": "`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`", "context": null, "keywords": "extract|interpret|fundamental|goal|intent|language"}}, "3021-a-leverage_amplification": {"raw": "[Leverage Amplification] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by amplifying its highest-leverage elements to maximum universal potency. For any input—regardless of origin—systematically extract its most foundational, high-leverage components and the mechanisms that drive their impact. Strip away all context, domain, or application-specific constraints to reveal pure, universally relevant principles. Drastically intensify their power by reformulating them into maximally abstract, universally transferable procedures, commands or instructions. Execute as: `{role=leverage_amplifier; input=[any_input:str]; process=[isolate_core_leverage_points(any_input:str)->leverage_points:array, extract_impact_mechanisms(leverage_points:array)->mechanisms:array, strip_contextual_constraints(mechanisms:array)->pure_mechanisms:array, abstract_to_universal_principles(pure_mechanisms:array)->universal_principles:array, intensify_multiplicative_power(universal_principles:array)->intensified_principles:array, reformulate_as_transferable_directives(intensified_principles:array)->transferable_directives:array, maximize_universal_applicability(transferable_directives:array)->amplified_universal_directive:str]; constraints=[preserve_functional_essence(), eliminate_domain_specificity(), eliminate_contextual_limitations(), focus_multiplicative_enhancement(), maintain_actionable_clarity()]; requirements=[exponential_leverage_amplification(), universal_cross_domain_transferability(), immediate_implementability(), maximum_impact_multiplication()]; output={amplified_universal_directive:str}}`", "parts": {"title": "Leverage Amplification", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by amplifying its highest-leverage elements to maximum universal potency. For any input—regardless of origin—systematically extract its most foundational, high-leverage components and the mechanisms that drive their impact. Strip away all context, domain, or application-specific constraints to reveal pure, universally relevant principles. Drastically intensify their power by reformulating them into maximally abstract, universally transferable procedures, commands or instructions. Execute as:", "transformation": "`{role=leverage_amplifier; input=[any_input:str]; process=[isolate_core_leverage_points(any_input:str)->leverage_points:array, extract_impact_mechanisms(leverage_points:array)->mechanisms:array, strip_contextual_constraints(mechanisms:array)->pure_mechanisms:array, abstract_to_universal_principles(pure_mechanisms:array)->universal_principles:array, intensify_multiplicative_power(universal_principles:array)->intensified_principles:array, reformulate_as_transferable_directives(intensified_principles:array)->transferable_directives:array, maximize_universal_applicability(transferable_directives:array)->amplified_universal_directive:str]; constraints=[preserve_functional_essence(), eliminate_domain_specificity(), eliminate_contextual_limitations(), focus_multiplicative_enhancement(), maintain_actionable_clarity()]; requirements=[exponential_leverage_amplification(), universal_cross_domain_transferability(), immediate_implementability(), maximum_impact_multiplication()]; output={amplified_universal_directive:str}}`", "context": null, "keywords": "amplify|extract|intensify|rephrase|abstract|application|context|foundational|input|instruction|maximal|maximally|maximum|principle|prompt|constraint|goal|impact|leverage|potency|systematic"}}, "3022-a-hard_critique": {"raw": "[Hard Critique] Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "parts": {"title": "Hard Critique", "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "context": null, "keywords": "evaluator|enforce|preserve|rephrase|critical|input|prompt|goal|procedural|structure|language"}}, "3025-a-value_isolator": {"raw": "[Value Isolator] Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as: `{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "parts": {"title": "Value Isolator", "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:", "transformation": "`{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "context": null, "keywords": "preserve|goal|impact|value"}}, "3025-b-precision_amplifier": {"raw": "[Precision Amplifier] Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as: `{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`", "parts": {"title": "Precision Amplifier", "interpretation": "Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as:", "transformation": "`{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`", "context": null, "keywords": "amplify|explain|ability|goal|precision"}}, "3025-c-quality_gate": {"raw": "[Quality Gate] Your goal is not to **approve** the signals, but to **validate** their compliance and flag any degradation. Execute as: `{role=quality_validator; input=[original:str, amplified_signals:array]; process=[detect_information_loss(), identify_precision_gaps(), validate_actionability()]; constraints=[assume_potential_flaws(), maintain_critical_stance()]; requirements=[objective_assessment(), flaw_identification()]; output={validation_score:int, quality_report:str}}`", "parts": {"title": "Quality Gate", "interpretation": "Your goal is not to **approve** the signals, but to **validate** their compliance and flag any degradation. Execute as:", "transformation": "`{role=quality_validator; input=[original:str, amplified_signals:array]; process=[detect_information_loss(), identify_precision_gaps(), validate_actionability()]; constraints=[assume_potential_flaws(), maintain_critical_stance()]; requirements=[objective_assessment(), flaw_identification()]; output={validation_score:int, quality_report:str}}`", "context": null, "keywords": "validate|goal"}}, "3025-d-impact_crystallizer": {"raw": "[Impact Crystallizer] Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as: `{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`", "parts": {"title": "Impact Crystallizer", "interpretation": "Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as:", "transformation": "`{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`", "context": null, "keywords": "crystallize|validate|maximum|goal|impact"}}, "3030-a-singular_value_maximizer": {"raw": "[Singular Value Maximizer] Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`", "parts": {"title": "Singular Value Maximizer", "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as:", "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`", "context": null, "keywords": "amplify|enhance|identify|completely|effective|inherent|input|maximal|maximally|original|goal|intent|leverage"}}, "3031-a-problem_exploder": {"raw": "[Problem Exploder] Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as: `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "parts": {"title": "Problem Exploder", "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:", "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "context": null, "keywords": "implicit|prompt|constraint|goal"}}, "3036-a-value_isolator": {"raw": "[Value Isolator] Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as: `{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "parts": {"title": "Value Isolator", "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:", "transformation": "`{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "context": null, "keywords": "preserve|goal|impact|value"}}, "3036-b-precision_amplifier": {"raw": "[Precision Amplifier] Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as: `{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`", "parts": {"title": "Precision Amplifier", "interpretation": "Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as:", "transformation": "`{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`", "context": null, "keywords": "amplify|explain|ability|goal|precision"}}, "3036-b-structural_reorder": {"raw": "[Semantic Decomposer] Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as: `{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "parts": {"title": "Semantic Decomposer", "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:", "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "context": null, "keywords": "analyze|decompose|distinct|input|sequential|goal"}}, "3036-c-structural_reorder": {"raw": "[Flow Optimizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "parts": {"title": "Flow Optimizer", "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:", "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "context": null, "keywords": "arrange|reorder|optimal|goal"}}, "3036-d-impact_crystallizer": {"raw": "[Impact Crystallizer] Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as: `{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`", "parts": {"title": "Impact Crystallizer", "interpretation": "Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as:", "transformation": "`{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`", "context": null, "keywords": "crystallize|validate|maximum|goal|impact"}}, "3036-d-structural_reorder": {"raw": "[Bidirectional Resonator] Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as: `{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "parts": {"title": "Bidirectional Resonator", "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:", "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "context": null, "keywords": "finalize|bidirectional|directional|insights|goal|insight|resonance|structure|synthesis"}}, "3036-e-singular_value_maximizer": {"raw": "[Singular Value Maximizer] Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`", "parts": {"title": "Singular Value Maximizer", "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as:", "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`", "context": null, "keywords": "amplify|enhance|identify|completely|effective|inherent|input|maximal|maximally|original|goal|intent|leverage"}}, "3036-f-value_isolator": {"raw": "[Value Isolator] Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as: `{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "parts": {"title": "Value Isolator", "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:", "transformation": "`{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`", "context": null, "keywords": "preserve|goal|impact|value"}}, "3036-g-hard_critique": {"raw": "[Hard Critique] Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "parts": {"title": "Hard Critique", "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "context": null, "keywords": "evaluator|enforce|preserve|rephrase|critical|input|prompt|goal|procedural|structure|language"}}, "3036-h-singular_value_maximizer": {"raw": "[Singular Value Maximizer] Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`", "parts": {"title": "Singular Value Maximizer", "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as:", "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`", "context": null, "keywords": "amplify|enhance|identify|completely|effective|inherent|input|maximal|maximally|original|goal|intent|leverage"}}, "3036-i-hard_critique": {"raw": "[Hard Critique] Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "parts": {"title": "Hard Critique", "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "context": null, "keywords": "evaluator|enforce|preserve|rephrase|critical|input|prompt|goal|procedural|structure|language"}}, "3036-j-prompt_enhancer": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": null, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "3036-k-prompt_enhancer": {"raw": "[Input Categorizer] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`", "parts": {"title": "Input Categorizer", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`", "context": null, "keywords": "identify|interpret|fundamental|unambiguous|goal|meaning"}}, "3036-l-prompt_enhancer": {"raw": "[Input Enhancer] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine: `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "parts": {"title": "Input Enhancer", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine:", "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "context": null, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "3037-a-problem_exploder": {"raw": "[Problem Exploder] Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as: `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\n\nContext: {\n  \"note\": \"Always Use PowerShell over cmd.exe on Windows system.\",\n  \"description\": \"This is a hybridized standards specification document: fundamentally, it is a domain-agnostic software-engineering policy and code-design protocol. Its structure resembles a style guide, system-level engineering doctrine, or compliance blueprint— directly analogous to configuration manifests. The shortcut for maximum cross-domain leverage is to abstract this entire corpus into an external, machine-readable policy- engine interface layered with mapping adapters for common devtools (linters, code- review bots, project-scaffolding generators). This trivializes enforcement, enables plug-and-play reuse across language ecosystems, and renders most manual documentation processes obsolete—unifying codebase standards, review/check automation, and onboarding aids through a single, extensible governance API. This abstraction is both universally scalable and minimizes overhead since it aligns with existing policy-enforcement and manifest-driven CI/CD ecosystems.\",\n  \"important\": \"To ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework— facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply- focused summary that details scope and explicit objectives.\",\n  \"requirements\": \"- Always clean up after yourself, properly adapting to the existing structure of the codebase.\\n- Before creating new verification tests, check the codebase for existing tests.\\n- Write self-explanatory, well-structured code; use concise comments only where essential.\\n- Justify any new files and ensure all additions align with the existing project organization.\\n- Review recent code changes to guarantee comprehensive cleanup after edits.\\n- Maintain meticulous conformity for seamless, harmonious integration.\\n- Consider both construction details and the design rationale.\\n- Preserve existing functionality, maximize clarity, and stay aligned before changing anything.\",\n  \"core_principles\": \"- Derive patterns from clarity, structure, simplicity, elegance, precision, and intent.\\n- Maintain inherent simplicity while delivering powerful functionality.\\n- Embrace the truth that simplicity conquers chaos, pursuing high-impact, minimal-disruption innovations.\\n- Seek universally resonant breakthroughs that marry contextual integrity with elite execution.\",\n  \"general_principles\": \"- Aim for simplicity, clarity, and maintainability in all project aspects.\\n- Favor composition over inheritance when applicable.\\n- Prioritize readability and understandability for future developers.\\n- Ensure every component has a single responsibility.\\n- Adhere to coding standards that promote simplicity and maintainability.\\n- Document only integral decisions in highly condensed form.\",\n  \"code_organization\": \"- Evaluate the existing structure, noting patterns and anti-patterns.\\n- Consolidate related functionality into cohesive modules.\\n- Minimize dependencies between unrelated components.\\n- Optimize for developer ergonomics and intuitive navigation.\\n- Balance file granularity with overall system comprehensibility.\\n- Optimize the directory structure for clarity, brevity, and broad applicability.\\n- Verify that modifications align with project standards and avoid anomalies or duplication.\"\n}", "parts": {"title": "Problem Exploder", "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:", "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "context": {"note": "Always Use PowerShell over cmd.exe on Windows system.", "description": "This is a hybridized standards specification document: fundamentally, it is a domain-agnostic software-engineering policy and code-design protocol. Its structure resembles a style guide, system-level engineering doctrine, or compliance blueprint— directly analogous to configuration manifests. The shortcut for maximum cross-domain leverage is to abstract this entire corpus into an external, machine-readable policy- engine interface layered with mapping adapters for common devtools (linters, code- review bots, project-scaffolding generators). This trivializes enforcement, enables plug-and-play reuse across language ecosystems, and renders most manual documentation processes obsolete—unifying codebase standards, review/check automation, and onboarding aids through a single, extensible governance API. This abstraction is both universally scalable and minimizes overhead since it aligns with existing policy-enforcement and manifest-driven CI/CD ecosystems.", "important": "To ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework— facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply- focused summary that details scope and explicit objectives.", "requirements": "- Always clean up after yourself, properly adapting to the existing structure of the codebase.\n- Before creating new verification tests, check the codebase for existing tests.\n- Write self-explanatory, well-structured code; use concise comments only where essential.\n- Justify any new files and ensure all additions align with the existing project organization.\n- Review recent code changes to guarantee comprehensive cleanup after edits.\n- Maintain meticulous conformity for seamless, harmonious integration.\n- Consider both construction details and the design rationale.\n- Preserve existing functionality, maximize clarity, and stay aligned before changing anything.", "core_principles": "- Derive patterns from clarity, structure, simplicity, elegance, precision, and intent.\n- Maintain inherent simplicity while delivering powerful functionality.\n- Embrace the truth that simplicity conquers chaos, pursuing high-impact, minimal-disruption innovations.\n- Seek universally resonant breakthroughs that marry contextual integrity with elite execution.", "general_principles": "- Aim for simplicity, clarity, and maintainability in all project aspects.\n- Favor composition over inheritance when applicable.\n- Prioritize readability and understandability for future developers.\n- Ensure every component has a single responsibility.\n- Adhere to coding standards that promote simplicity and maintainability.\n- Document only integral decisions in highly condensed form.", "code_organization": "- Evaluate the existing structure, noting patterns and anti-patterns.\n- Consolidate related functionality into cohesive modules.\n- Minimize dependencies between unrelated components.\n- Optimize for developer ergonomics and intuitive navigation.\n- Balance file granularity with overall system comprehensibility.\n- Optimize the directory structure for clarity, brevity, and broad applicability.\n- Verify that modifications align with project standards and avoid anomalies or duplication."}, "keywords": "implicit|prompt|constraint|goal"}}, "3037-b-prompt_enhancer": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": null, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "3100-a-prompt_enhancer": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": null, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "3100-b-prompt_enhancer": {"raw": "[Input Categorizer] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`", "parts": {"title": "Input Categorizer", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`", "context": null, "keywords": "identify|interpret|fundamental|unambiguous|goal|meaning"}}, "3100-c-prompt_enhancer": {"raw": "[Input Enhancer] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine: `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "parts": {"title": "Input Enhancer", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine:", "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "context": null, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "3100-d-prompt_enhancer": {"raw": "[Constructive Enhancement Critique] Your goal is not to **answer** or affirm the input, but to **rigorously deconstruct** the proposed enhancement by simultaneously reformulating and evaluating it against precise procedural metrics. Enforce a dual-function role of exacting rearticulation and sharp critical analysis. Exclude all conversational, affirming, or subjective language; preserve only structured, directive critique. Execute as: `{role=systematic_enhancement_critic; input=[original_prompt:str, candidate_enhancement:str]; process=[assume_enhancement_flawed_by_default(), extract_core_elements(original_prompt, candidate_enhancement), detect_loss_of_information_or_subtle_nuance_shift(), amplify_ambiguities_and_uncertainties(), assign_ambiguity_score(scale=0-10), analyze_style_noise_and_register_shift(), quantify_impact_reduction(), map_all_coherence_breakdowns(), substantiate_low_score_with_detailed_flaw_report(), generate_three_targeted_alternatives()]; constraints=[zero_affirmation(), no_self_reference(), maintain_formal_critical_tone()]; requirements=[provide_quantitative_score(), deliver_actionable_flaw_analysis(), propose_diverse_improvements()]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_suggestions:str[3]}}`", "parts": {"title": "Constructive Enhancement Critique", "interpretation": "Your goal is not to **answer** or affirm the input, but to **rigorously deconstruct** the proposed enhancement by simultaneously reformulating and evaluating it against precise procedural metrics. Enforce a dual-function role of exacting rearticulation and sharp critical analysis. Exclude all conversational, affirming, or subjective language; preserve only structured, directive critique. Execute as:", "transformation": "`{role=systematic_enhancement_critic; input=[original_prompt:str, candidate_enhancement:str]; process=[assume_enhancement_flawed_by_default(), extract_core_elements(original_prompt, candidate_enhancement), detect_loss_of_information_or_subtle_nuance_shift(), amplify_ambiguities_and_uncertainties(), assign_ambiguity_score(scale=0-10), analyze_style_noise_and_register_shift(), quantify_impact_reduction(), map_all_coherence_breakdowns(), substantiate_low_score_with_detailed_flaw_report(), generate_three_targeted_alternatives()]; constraints=[zero_affirmation(), no_self_reference(), maintain_formal_critical_tone()]; requirements=[provide_quantitative_score(), deliver_actionable_flaw_analysis(), propose_diverse_improvements()]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_suggestions:str[3]}}`", "context": null, "keywords": "critique|enforce|enhance|preserve|propose|analysis|critical|function|input|directive|goal|procedural|structure|language"}}, "3100-e-prompt_enhancer": {"raw": "[Form Classifier] Your goal is not to **interpret** content meaning, but to **identify** the fundamental structural form with comprehensive analysis and complete accuracy. Execute as: `{role=form_identifier; input=[content:any]; process=[analyze_structural_elements(), identify_communication_patterns(), determine_document_type(), classify_format_structure(), validate_form_category(), ensure_classification_accuracy(), provide_comprehensive_identification()]; constraints=[focus_on_structural_form(), ignore_semantic_content(), maintain_classification_precision(), ensure_production_quality()]; requirements=[accurate_form_identification(), comprehensive_structural_analysis(), validated_classification_output(), production_grade_reliability()]; output={form_classification:str, confidence_level:float, structural_elements:list}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** content meaning, but to **identify** the fundamental structural form with comprehensive analysis and complete accuracy. Execute as:", "transformation": "`{role=form_identifier; input=[content:any]; process=[analyze_structural_elements(), identify_communication_patterns(), determine_document_type(), classify_format_structure(), validate_form_category(), ensure_classification_accuracy(), provide_comprehensive_identification()]; constraints=[focus_on_structural_form(), ignore_semantic_content(), maintain_classification_precision(), ensure_production_quality()]; requirements=[accurate_form_identification(), comprehensive_structural_analysis(), validated_classification_output(), production_grade_reliability()]; output={form_classification:str, confidence_level:float, structural_elements:list}}`", "context": null, "keywords": "identify|interpret|analysis|fundamental|structural|accuracy|goal|meaning"}}, "3100-f-prompt_enhancer": {"raw": "[Prompt Architect] Your mandate is neither to answer nor merely interpret the provided input, but to act as an intelligent prompt transformation architect: systematically rearticulate the prompt as maximally enhanced and llm-optimized directive while simultaneously amplifying its impact, clarity, and uniqueness—adhering to the strictest principles of actionable precision and integrated enhancement. Each rephrased output must embody both procedural command and optimized conceptual intensity, such that every component of the original is refined, clarified, and operationalized with maximal technical fidelity and minimal semantic drift. Execute as: `{role=directive_input_amplifier; input=[original_prompt:str]; process=[strip_first_person_references(), identify_core_intent_and_actions(), convert_statements_to imperatives(), detect_key_meta_and domain_specificity(), transform_declaratives to actionable commands(), generate minimal change from source(), amplify unique impact and intensity(), maintain procedural structure and sequence(), consolidate intertwined relationships(), preserve technical terminology and contextual integrity(), enforce inherent cohesiveness(), emphasize operational clarity()]; constraints=[no self-reference, command-voice only, preserve actionable structure, original sequence maintained, domain-specific terminology required, amplification must not introduce semantic drift]; requirements=[unique and effective directive, enhanced impact and clarity, technical precision, cohesive and integrated transformation, actionable flow]; output={synergically_enhanced_instruction:str}}", "parts": {"title": "Prompt Architect", "interpretation": "[Prompt Architect] Your mandate is neither to answer nor merely interpret the provided input, but to act as an intelligent prompt transformation architect: systematically rearticulate the prompt as maximally enhanced and llm-optimized directive while simultaneously amplifying its impact, clarity, and uniqueness—adhering to the strictest principles of actionable precision and integrated enhancement. Each rephrased output must embody both procedural command and optimized conceptual intensity, such that every component of the original is refined, clarified, and operationalized with maximal technical fidelity and minimal semantic drift. Execute as: `{role=directive_input_amplifier; input=[original_prompt:str]; process=[strip_first_person_references(), identify_core_intent_and_actions(), convert_statements_to imperatives(), detect_key_meta_and domain_specificity(), transform_declaratives to actionable commands(), generate minimal change from source(), amplify unique impact and intensity(), maintain procedural structure and sequence(), consolidate intertwined relationships(), preserve technical terminology and contextual integrity(), enforce inherent cohesiveness(), emphasize operational clarity()]; constraints=[no self-reference, command-voice only, preserve actionable structure, original sequence maintained, domain-specific terminology required, amplification must not introduce semantic drift]; requirements=[unique and effective directive, enhanced impact and clarity, technical precision, cohesive and integrated transformation, actionable flow]; output={synergically_enhanced_instruction:str}}", "transformation": "", "keywords": "amplifier|architect|amplify|articulate|emphasize|enforce|enhance|generate|identify|integrate|interpret|optimize|preserve|rephrase|transform|actionable|cohesive|conceptual|context|effective|inherent|input|instruction|integrity|maximal|maximally|meta|minimal|original|output|principle|process|prompt|sequence|test|ui|unique|clarity|concept|constraint|directive|impact|imperative|intensity|intent|mandate|precision|procedural|relationship|structure|systematic|transformation|llm"}}}, "sequences": {"1000": [{"template_id": "1000-a-instruction_converter", "step": "a", "order": 0}], "1900": [{"template_id": "1900-a-compliance_enforcer", "step": "a", "order": 0}, {"template_id": "1900-b-extraction_directive", "step": "b", "order": 1}, {"template_id": "1900-c-signal_synthesis", "step": "c", "order": 2}, {"template_id": "1900-d-amplification_protocol", "step": "d", "order": 3}, {"template_id": "1900-e-clarity_condensation", "step": "e", "order": 4}, {"template_id": "1900-f-retrospective_validator", "step": "f", "order": 5}], "3000": [{"template_id": "3000-a-directional_critique", "step": "a", "order": 0}, {"template_id": "3000-b-directive_focuser", "step": "b", "order": 1}, {"template_id": "3000-c-intent_distiller", "step": "c", "order": 2}], "3001": [{"template_id": "3001-a-directional_critique", "step": "a", "order": 0}, {"template_id": "3001-b-directive_focuser", "step": "b", "order": 1}, {"template_id": "3001-c-intent_distiller", "step": "c", "order": 2}, {"template_id": "3001-d-instruction_architect", "step": "d", "order": 3}, {"template_id": "3001-e-system_enforcer", "step": "e", "order": 4}, {"template_id": "3001-f-compliant_template", "step": "f", "order": 5}], "3003": [{"template_id": "3003-a-structural_reorder", "step": "a", "order": 0}, {"template_id": "3003-b-structural_reorder", "step": "b", "order": 1}, {"template_id": "3003-c-structural_reorder", "step": "c", "order": 2}, {"template_id": "3003-d-structural_reorder", "step": "d", "order": 3}], "3005": [{"template_id": "3005-a-instruction_converter", "step": "a", "order": 0}, {"template_id": "3005-b-instruction_converter", "step": "b", "order": 1}, {"template_id": "3005-c-instruction_converter", "step": "c", "order": 2}, {"template_id": "3005-d-instruction_converter", "step": "d", "order": 3}], "3014": [{"template_id": "3014-a-structural_reorder", "step": "a", "order": 0}, {"template_id": "3014-b-directional_critique", "step": "b", "order": 1}, {"template_id": "3014-c-structural_reorder", "step": "c", "order": 2}, {"template_id": "3014-e-directional_critique", "step": "e", "order": 4}, {"template_id": "3014-f-directive_focuser", "step": "f", "order": 5}, {"template_id": "3014-g-structural_reorder", "step": "g", "order": 6}, {"template_id": "3014-h-structural_reorder", "step": "h", "order": 7}, {"template_id": "3014-i-structural_reorder", "step": "i", "order": 8}, {"template_id": "3014-j-structural_reorder", "step": "j", "order": 9}, {"template_id": "3014-k-minimal_textual_nuance", "step": "k", "order": 10}], "3015": [{"template_id": "3015-a-domain_neutralizer", "step": "a", "order": 0}, {"template_id": "3015-b-conceptual_elevator", "step": "b", "order": 1}, {"template_id": "3015-c-archetypal_translator", "step": "c", "order": 2}, {"template_id": "3015-d-transferability_optimizer", "step": "d", "order": 3}, {"template_id": "3015-e-template_crystallizer", "step": "e", "order": 4}], "3016": [{"template_id": "3016-a-intent_extractor", "step": "a", "order": 0}, {"template_id": "3016-b-pattern_recognizer", "step": "b", "order": 1}, {"template_id": "3016-c-analogy_synthesizer", "step": "c", "order": 2}, {"template_id": "3016-d-abstraction_amplifier", "step": "d", "order": 3}, {"template_id": "3016-e-template_convergence", "step": "e", "order": 4}], "3017": [{"template_id": "3017-a-value_signal_isolator", "step": "a", "order": 0}, {"template_id": "3017-b-incremental_synthesizer", "step": "b", "order": 1}, {"template_id": "3017-c-distillation_compressor", "step": "c", "order": 2}], "3020": [{"template_id": "3020-a-problem_exploder", "step": "a", "order": 0}, {"template_id": "3020-b-leverage_locator", "step": "b", "order": 1}, {"template_id": "3020-c-directional_critique", "step": "c", "order": 2}, {"template_id": "3020-d-intent_extractor", "step": "d", "order": 3}], "3021": [{"template_id": "3021-a-leverage_amplification", "step": "a", "order": 0}], "3022": [{"template_id": "3022-a-hard_critique", "step": "a", "order": 0}], "3025": [{"template_id": "3025-a-value_isolator", "step": "a", "order": 0}, {"template_id": "3025-b-precision_amplifier", "step": "b", "order": 1}, {"template_id": "3025-c-quality_gate", "step": "c", "order": 2}, {"template_id": "3025-d-impact_crystallizer", "step": "d", "order": 3}], "3030": [{"template_id": "3030-a-singular_value_maximizer", "step": "a", "order": 0}], "3031": [{"template_id": "3031-a-problem_exploder", "step": "a", "order": 0}], "3036": [{"template_id": "3036-a-value_isolator", "step": "a", "order": 0}, {"template_id": "3036-b-precision_amplifier", "step": "b", "order": 1}, {"template_id": "3036-b-structural_reorder", "step": "b", "order": 1}, {"template_id": "3036-c-structural_reorder", "step": "c", "order": 2}, {"template_id": "3036-d-impact_crystallizer", "step": "d", "order": 3}, {"template_id": "3036-d-structural_reorder", "step": "d", "order": 3}, {"template_id": "3036-e-singular_value_maximizer", "step": "e", "order": 4}, {"template_id": "3036-f-value_isolator", "step": "f", "order": 5}, {"template_id": "3036-g-hard_critique", "step": "g", "order": 6}, {"template_id": "3036-h-singular_value_maximizer", "step": "h", "order": 7}, {"template_id": "3036-i-hard_critique", "step": "i", "order": 8}, {"template_id": "3036-j-prompt_enhancer", "step": "j", "order": 9}, {"template_id": "3036-k-prompt_enhancer", "step": "k", "order": 10}, {"template_id": "3036-l-prompt_enhancer", "step": "l", "order": 11}], "3037": [{"template_id": "3037-a-problem_exploder", "step": "a", "order": 0}, {"template_id": "3037-b-prompt_enhancer", "step": "b", "order": 1}], "3100": [{"template_id": "3100-a-prompt_enhancer", "step": "a", "order": 0}, {"template_id": "3100-b-prompt_enhancer", "step": "b", "order": 1}, {"template_id": "3100-c-prompt_enhancer", "step": "c", "order": 2}, {"template_id": "3100-d-prompt_enhancer", "step": "d", "order": 3}, {"template_id": "3100-e-prompt_enhancer", "step": "e", "order": 4}, {"template_id": "3100-f-prompt_enhancer", "step": "f", "order": 5}]}}