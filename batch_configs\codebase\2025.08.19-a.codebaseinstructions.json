{"name": "v01.codebaseinstructions", "description": "Templates that rephrase inputs into instructions optimized for autonomous coding agents (Cursor, VS Code agent mode, Cline, etc)", "config": {"chain_mode": true, "show_inputs": false, "show_system_instructions": false, "show_responses": true, "minified_output": true, "concurrent": true}, "defaults": {"prompt": "\"can't tell if it's on purpose. #engineeredchaos\": what would be the perfect hashtag according to the depth of <PERSON><PERSON><PERSON>'s character while also accounting for virality (to replace \"#engineeredchaos\")?", "models": ["gpt-3.5-turbo"]}, "jobs": {"a-01_": {"sequence": "1900|3000|3100"}, "a-02_": {"sequence": "2001"}, "a-03_": {"sequence": "2004"}, "a-04_": {"sequence": "3000"}, "a-05_": {"sequence": "3000|2004-d|3000"}, "a-06_": {"sequence": "3000|3100-a-c"}, "a-07_": {"sequence": "3001"}, "a-08_": {"sequence": "3001|3100"}, "a-09_": {"sequence": "3003-a|3001-a-b|3100-d|3003|3100-d|3100-a-c"}, "a-10_": {"sequence": "3003-a|3100-d|3001-a-b|3003|3100-d|3100-a-c"}, "a-11_": {"sequence": "3003|3030|3022|3100-a-c|3030"}, "a-12_": {"sequence": "3003|3030|3025-a-b|3022|3025-d|3030|3022|3100-a-c|3030"}, "a-13_": {"sequence": "3020-b|3021-a|3022|3005|3020-c|3021|3020-a|3100-a-c|3016-a|3015-e|3017"}, "a-14_": {"sequence": "3030|3000"}, "a-15_": {"sequence": "3030|3022|3000"}, "a-16_": {"sequence": "3030|3022|3000|3030|3022|3000"}, "a-17_": {"sequence": "3031|3014"}, "a-18_": {"sequence": "3036"}, "a-19_": {"sequence": "3036|3022|3020-c"}, "a-20_": {"sequence": "3036|3022|3020-c|3036|3022|3036"}, "a-21_": {"sequence": "3036|3022|3036|3022|3036"}, "a-22_": {"sequence": "3037|3022|3037"}, "a-23_": {"sequence": "3100"}, "a-24_": {"sequence": "3100-a-c"}}}