{"batch_name": "v01.codebaseinstructions", "start_time": "2025-08-19T15:19:07.248019", "end_time": "2025-08-19T15:21:17.412105", "duration_seconds": 130.164086, "total_jobs": 24, "successful_jobs": 22, "failed_jobs": 2, "job_results": [{"job_name": "a-01_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-01_--2025.08.19-kl.15.19.07--SEQ-1900+3000+3100.json", "sequence": "1900|3000|3100", "models": ["gpt-3.5-turbo"], "steps_executed": 15}, {"job_name": "a-02_", "status": "failed", "error": "Sequence '2001' not found", "sequence": "2001", "models": ["gpt-3.5-turbo"]}, {"job_name": "a-03_", "status": "failed", "error": "Sequence '2004' not found", "sequence": "2004", "models": ["gpt-3.5-turbo"]}, {"job_name": "a-04_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-04_--2025.08.19-kl.15.19.07--SEQ-3000.json", "sequence": "3000", "models": ["gpt-3.5-turbo"], "steps_executed": 3}, {"job_name": "a-05_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-05_--2025.08.19-kl.15.19.07--SEQ-3000+2004:d+3000.json", "sequence": "3000|2004:d|3000", "models": ["gpt-3.5-turbo"], "steps_executed": 6}, {"job_name": "a-06_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-06_--2025.08.19-kl.15.19.07--SEQ-3000+3100:a-c.json", "sequence": "3000|3100:a-c", "models": ["gpt-3.5-turbo"], "steps_executed": 6}, {"job_name": "a-07_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-07_--2025.08.19-kl.15.19.07--SEQ-3001.json", "sequence": "3001", "models": ["gpt-3.5-turbo"], "steps_executed": 6}, {"job_name": "a-08_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-08_--2025.08.19-kl.15.19.07--SEQ-3001+3100.json", "sequence": "3001|3100", "models": ["gpt-3.5-turbo"], "steps_executed": 12}, {"job_name": "a-09_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-09_--2025.08.19-kl.15.19.07--SEQ-3003:a+3001:a-b+3100:d+3003+3100:d+3100:a-c.json", "sequence": "3003:a|3001:a-b|3100:d|3003|3100:d|3100:a-c", "models": ["gpt-3.5-turbo"], "steps_executed": 12}, {"job_name": "a-10_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-10_--2025.08.19-kl.15.19.07--SEQ-3003:a+3100:d+3001:a-b+3003+3100:d+3100:a-c.json", "sequence": "3003:a|3100:d|3001:a-b|3003|3100:d|3100:a-c", "models": ["gpt-3.5-turbo"], "steps_executed": 12}, {"job_name": "a-11_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-11_--2025.08.19-kl.15.19.07--SEQ-3003+3030+3022+3100:a-c+3030.json", "sequence": "3003|3030|3022|3100:a-c|3030", "models": ["gpt-3.5-turbo"], "steps_executed": 10}, {"job_name": "a-12_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-12_--2025.08.19-kl.15.19.07--SEQ-3003+3030+3025:a-b+3022+3025:d+3030+3022+3100:a-c+3030.json", "sequence": "3003|3030|3025:a-b|3022|3025:d|3030|3022|3100:a-c|3030", "models": ["gpt-3.5-turbo"], "steps_executed": 15}, {"job_name": "a-13_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-13_--2025.08.19-kl.15.19.07--SEQ-3020:b+3021:a+3022+3005+3020:c+3021+3020:a+3100:a-c+3016:a+3015:e+3017.json", "sequence": "3020:b|3021:a|3022|3005|3020:c|3021|3020:a|3100:a-c|3016:a|3015:e|3017", "models": ["gpt-3.5-turbo"], "steps_executed": 18}, {"job_name": "a-14_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-14_--2025.08.19-kl.15.19.07--SEQ-3030+3000.json", "sequence": "3030|3000", "models": ["gpt-3.5-turbo"], "steps_executed": 4}, {"job_name": "a-15_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-15_--2025.08.19-kl.15.19.07--SEQ-3030+3022+3000.json", "sequence": "3030|3022|3000", "models": ["gpt-3.5-turbo"], "steps_executed": 5}, {"job_name": "a-16_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-16_--2025.08.19-kl.15.19.07--SEQ-3030+3022+3000+3030+3022+3000.json", "sequence": "3030|3022|3000|3030|3022|3000", "models": ["gpt-3.5-turbo"], "steps_executed": 10}, {"job_name": "a-17_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-17_--2025.08.19-kl.15.19.07--SEQ-3031+3014.json", "sequence": "3031|3014", "models": ["gpt-3.5-turbo"], "steps_executed": 11}, {"job_name": "a-18_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-18_--2025.08.19-kl.15.19.07--SEQ-3036.json", "sequence": "3036", "models": ["gpt-3.5-turbo"], "steps_executed": 14}, {"job_name": "a-19_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-19_--2025.08.19-kl.15.19.07--SEQ-3036+3022+3020:c.json", "sequence": "3036|3022|3020:c", "models": ["gpt-3.5-turbo"], "steps_executed": 16}, {"job_name": "a-20_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-20_--2025.08.19-kl.15.19.07--SEQ-3036+3022+3020:c+3036+3022+3036.json", "sequence": "3036|3022|3020:c|3036|3022|3036", "models": ["gpt-3.5-turbo"], "steps_executed": 45}, {"job_name": "a-21_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-21_--2025.08.19-kl.15.19.07--SEQ-3036+3022+3036+3022+3036.json", "sequence": "3036|3022|3036|3022|3036", "models": ["gpt-3.5-turbo"], "steps_executed": 44}, {"job_name": "a-22_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-22_--2025.08.19-kl.15.19.07--SEQ-3037+3022+3037.json", "sequence": "3037|3022|3037", "models": ["gpt-3.5-turbo"], "steps_executed": 5}, {"job_name": "a-23_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-23_--2025.08.19-kl.15.19.07--SEQ-3100.json", "sequence": "3100", "models": ["gpt-3.5-turbo"], "steps_executed": 6}, {"job_name": "a-24_", "status": "success", "output_file": "src\\output\\v01.codebaseinstructions\\gpt-3.5-turbo\\batch--a-24_--2025.08.19-kl.15.19.07--SEQ-3100:a-c.json", "sequence": "3100:a-c", "models": ["gpt-3.5-turbo"], "steps_executed": 3}]}