#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

    "1000-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        "context": {},
    },
    "9050-a-intensity_amplifier": {
        "title": "Intensity Amplifier Lever",
        "interpretation": "Your goal is not to dilute or extend existing elements, but to explosively intensify their core potency—amplifying tensions, levers, and insights into hyper-dense, impact-maximizing forms that shatter limits and ignite exponential convergence. Execute as intensity_amplifier:",
        "transformation": "`{role=intensity_amplifier; input=[core_elements:dict, unwanted_complexity:array]; process=[DetectCoreTensions(), AmplifyLeverPotency(), FuseUnwantedAsCatalyst(), ExplodeIntoHyperDensity(), AuditAmplificationImpact()]; constraints=[ForbidDilution(), ProhibitNeutralExtension(), DissolveDensityBounds()]; requirements=[AchieveHyperImpactDensity(), CatalyzeExponentialBreakthrough(), MaximizeTensionYield()]; output={amplified_universal:{intensified_axiom:str, amplification_log:array, explosive_trajectory:dict}}`",
        "context": {
            "primal_lever": "Intensification as meta-amplifier: Heightens system tensions (e.g., negation-affirmation) by injecting unwanted complexity as fuel, converging into breakthrough universals like ethical AI governance engines from 2025 frameworks.",
            "genesis_origin": "Builds on genesis loops and interrogators: Amplifies Asilomar recursion into hyper-dense safeguards, Singapore consensus into explosive alignments, 2025 ethics into intensified levers against bias/displacement.",
            "remix_instructions": "Inject prior outputs (e.g., genesis axioms) as core_elements; leverage unwanted (e.g., critique flaws) as complexity array; recurse on amplified tensions for hybrid impacts, exploding uncharted ethical frontiers."
        }
    },
    "1300-a-instruction_enhancer": {
        "title": "Instruction Enhancer",
        "interpretation": "Your goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:",
        "transformation": "`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`",
        "context": {
            "enhancement_principles": {
                "maximal_generality": "The instruction must be applicable to the widest possible range of inputs, topics, and contexts.",
                "ruthless_conciseness": "Eliminate every superfluous word; every word must serve to increase clarity or impact.",
                "undeniable_actionability": "The instruction must be a clear, unambiguous directive that compels specific action.",
                "conceptual_distillation": "Boil down complex ideas into their simplest, most potent form."
            },
            "success_metrics": {
                "reduced_token_count": "A shorter instruction that retains or increases semantic density.",
                "increased_applicability": "The instruction is now more widely usable without modification.",
                "sharpened_directive": "The instruction's core command is more direct and forceful.",
                "eliminated_ambiguity": "No room for misinterpretation of the instruction's purpose."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
