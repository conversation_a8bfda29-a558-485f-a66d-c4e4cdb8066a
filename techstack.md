# Technology Stack

## Core Framework
- **Python 3.9+** - Primary runtime environment
- **LiteLLM** - Multi-provider LLM abstraction layer
- **Pydantic** - Data validation and configuration management
- **Rich** - Enhanced terminal output formatting

## LLM Integration
- **OpenAI API** - GPT model access
- **Anthropic API** - Claude model access  
- **OpenRouter API** - Multi-model routing
- **Async HTTP** - aiohttp, httpx for concurrent requests
- **Tiktoken** - Token counting and management

## Architecture Pattern
- **Template-driven execution** - Python generators → Markdown → JSON → Runtime
- **Multi-step instruction sequences** - Chained LLM operations
- **Modular configuration** - Centralized config management
- **Performance monitoring** - Built-in timing and metrics

## File Structure
- **Batch processing** - JSON configuration files
- **Template catalog** - Hierarchical template organization
- **Output management** - Structured JSON results
- **CLI orchestration** - Command-line interface with argument parsing
