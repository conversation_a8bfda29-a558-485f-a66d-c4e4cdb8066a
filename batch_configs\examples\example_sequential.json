{"name": "v01.sequential_example", "description": "Sequential batch execution example", "config": {"chain_mode": false, "show_inputs": true, "show_system_instructions": false, "show_responses": true, "minified_output": true, "concurrent": false}, "defaults": {"prompt": "Process this information step by step", "models": ["gpt-3.5-turbo"]}, "jobs": {"step-1": {"sequence": "1000"}, "step-2": {"sequence": "1400"}, "step-3": {"sequence": "1450"}}}